/**
 * Enhanced Logger for Black-G CLI
 * Provides comprehensive logging, monitoring, and alerting capabilities
 */

const fs = require('fs');
const path = require('path');
const { EventEmitter } = require('events');

class EnhancedLogger extends EventEmitter {
    constructor(options = {}) {
        super();
        
        this.options = {
            logLevel: options.logLevel || process.env.BLACK_G_LOG_LEVEL || 'info',
            logDir: options.logDir || './logs',
            maxFileSize: options.maxFileSize || 10 * 1024 * 1024, // 10MB
            maxFiles: options.maxFiles || 5,
            enableConsole: options.enableConsole !== false,
            enableFile: options.enableFile !== false,
            enableMetrics: options.enableMetrics !== false,
            enableAlerts: options.enableAlerts !== false
        };

        this.logLevels = {
            error: 0,
            warn: 1,
            info: 2,
            debug: 3,
            trace: 4
        };

        this.currentLogLevel = this.logLevels[this.options.logLevel] || 2;
        
        // Performance metrics
        this.metrics = {
            startTime: Date.now(),
            totalRequests: 0,
            successfulScans: 0,
            failedScans: 0,
            toolExecutions: {},
            errorCounts: {},
            performanceData: []
        };

        // Alert thresholds
        this.alertThresholds = {
            errorRate: 0.1, // 10% error rate
            responseTime: 30000, // 30 seconds
            failureCount: 5, // 5 consecutive failures
            memoryUsage: 0.9 // 90% memory usage
        };

        this.consecutiveFailures = 0;
        
        this.initializeLogger();
    }

    initializeLogger() {
        // Create logs directory if it doesn't exist
        if (this.options.enableFile && !fs.existsSync(this.options.logDir)) {
            fs.mkdirSync(this.options.logDir, { recursive: true });
        }

        // Set up log rotation
        if (this.options.enableFile) {
            this.setupLogRotation();
        }

        // Start metrics collection
        if (this.options.enableMetrics) {
            this.startMetricsCollection();
        }

        // Set up alert monitoring
        if (this.options.enableAlerts) {
            this.startAlertMonitoring();
        }

        this.info('Enhanced Logger initialized', {
            logLevel: this.options.logLevel,
            enableFile: this.options.enableFile,
            enableMetrics: this.options.enableMetrics,
            enableAlerts: this.options.enableAlerts
        });
    }

    log(level, message, metadata = {}) {
        if (this.logLevels[level] > this.currentLogLevel) {
            return;
        }

        const timestamp = new Date().toISOString();
        const logEntry = {
            timestamp,
            level: level.toUpperCase(),
            message,
            metadata,
            pid: process.pid,
            memory: process.memoryUsage(),
            uptime: process.uptime()
        };

        // Console output
        if (this.options.enableConsole) {
            this.logToConsole(logEntry);
        }

        // File output
        if (this.options.enableFile) {
            this.logToFile(logEntry);
        }

        // Update metrics
        if (this.options.enableMetrics) {
            this.updateMetrics(level, metadata);
        }

        // Check for alerts
        if (this.options.enableAlerts) {
            this.checkAlerts(level, metadata);
        }

        // Emit event for external listeners
        this.emit('log', logEntry);
    }

    error(message, metadata = {}) {
        this.log('error', message, metadata);
    }

    warn(message, metadata = {}) {
        this.log('warn', message, metadata);
    }

    info(message, metadata = {}) {
        this.log('info', message, metadata);
    }

    debug(message, metadata = {}) {
        this.log('debug', message, metadata);
    }

    trace(message, metadata = {}) {
        this.log('trace', message, metadata);
    }

    // Specialized logging methods for Black-G CLI
    logScanStart(target, sessionId) {
        this.metrics.totalRequests++;
        this.info('Scan session started', {
            target,
            sessionId,
            type: 'scan_start'
        });
    }

    logScanComplete(target, sessionId, success, duration, results) {
        if (success) {
            this.metrics.successfulScans++;
            this.consecutiveFailures = 0;
        } else {
            this.metrics.failedScans++;
            this.consecutiveFailures++;
        }

        this.metrics.performanceData.push({
            timestamp: Date.now(),
            duration,
            success,
            target
        });

        this.info('Scan session completed', {
            target,
            sessionId,
            success,
            duration,
            resultsCount: results?.length || 0,
            type: 'scan_complete'
        });
    }

    logToolExecution(tool, command, success, duration, error = null) {
        if (!this.metrics.toolExecutions[tool]) {
            this.metrics.toolExecutions[tool] = {
                total: 0,
                successful: 0,
                failed: 0,
                totalDuration: 0
            };
        }

        this.metrics.toolExecutions[tool].total++;
        this.metrics.toolExecutions[tool].totalDuration += duration;

        if (success) {
            this.metrics.toolExecutions[tool].successful++;
            this.debug('Tool execution successful', {
                tool,
                command,
                duration,
                type: 'tool_execution'
            });
        } else {
            this.metrics.toolExecutions[tool].failed++;
            this.error('Tool execution failed', {
                tool,
                command,
                duration,
                error: error?.message || 'Unknown error',
                type: 'tool_execution'
            });
        }
    }

    logAIInteraction(prompt, response, duration, tokens = null) {
        this.debug('AI interaction completed', {
            promptLength: prompt?.length || 0,
            responseLength: response?.length || 0,
            duration,
            tokens,
            type: 'ai_interaction'
        });
    }

    logSecurityEvent(eventType, severity, details) {
        this.warn('Security event detected', {
            eventType,
            severity,
            details,
            type: 'security_event'
        });

        // Emit security alert
        this.emit('security_alert', {
            eventType,
            severity,
            details,
            timestamp: new Date().toISOString()
        });
    }

    logToConsole(logEntry) {
        const colors = {
            ERROR: '\x1b[31m',   // Red
            WARN: '\x1b[33m',    // Yellow
            INFO: '\x1b[36m',    // Cyan
            DEBUG: '\x1b[35m',   // Magenta
            TRACE: '\x1b[37m',   // White
            RESET: '\x1b[0m'
        };

        const color = colors[logEntry.level] || colors.INFO;
        const timestamp = logEntry.timestamp.substring(11, 19); // HH:MM:SS
        
        console.log(
            `${color}[${timestamp}] ${logEntry.level}${colors.RESET} ${logEntry.message}`,
            Object.keys(logEntry.metadata).length > 0 ? logEntry.metadata : ''
        );
    }

    logToFile(logEntry) {
        const logFile = path.join(this.options.logDir, `black-g-${logEntry.level.toLowerCase()}.log`);
        const logLine = JSON.stringify(logEntry) + '\n';

        try {
            fs.appendFileSync(logFile, logLine);
        } catch (error) {
            console.error('Failed to write to log file:', error.message);
        }
    }

    setupLogRotation() {
        // Check log file sizes and rotate if necessary
        setInterval(() => {
            this.rotateLogsIfNeeded();
        }, 60000); // Check every minute
    }

    rotateLogsIfNeeded() {
        const logLevels = ['error', 'warn', 'info', 'debug', 'trace'];
        
        for (const level of logLevels) {
            const logFile = path.join(this.options.logDir, `black-g-${level}.log`);
            
            if (fs.existsSync(logFile)) {
                const stats = fs.statSync(logFile);
                
                if (stats.size > this.options.maxFileSize) {
                    this.rotateLogFile(logFile, level);
                }
            }
        }
    }

    rotateLogFile(logFile, level) {
        try {
            // Move current log to .1, .2, etc.
            for (let i = this.options.maxFiles - 1; i >= 1; i--) {
                const oldFile = `${logFile}.${i}`;
                const newFile = `${logFile}.${i + 1}`;
                
                if (fs.existsSync(oldFile)) {
                    if (i === this.options.maxFiles - 1) {
                        fs.unlinkSync(oldFile); // Delete oldest
                    } else {
                        fs.renameSync(oldFile, newFile);
                    }
                }
            }
            
            // Move current log to .1
            fs.renameSync(logFile, `${logFile}.1`);
            
            this.info('Log file rotated', { level, file: logFile });
        } catch (error) {
            this.error('Log rotation failed', { level, file: logFile, error: error.message });
        }
    }

    startMetricsCollection() {
        // Collect metrics every 30 seconds
        setInterval(() => {
            this.collectSystemMetrics();
        }, 30000);
    }

    collectSystemMetrics() {
        const memUsage = process.memoryUsage();
        const cpuUsage = process.cpuUsage();
        
        const metrics = {
            timestamp: Date.now(),
            memory: {
                rss: memUsage.rss,
                heapUsed: memUsage.heapUsed,
                heapTotal: memUsage.heapTotal,
                external: memUsage.external
            },
            cpu: cpuUsage,
            uptime: process.uptime(),
            activeHandles: process._getActiveHandles().length,
            activeRequests: process._getActiveRequests().length
        };

        this.trace('System metrics collected', { metrics, type: 'system_metrics' });
        this.emit('metrics', metrics);
    }

    updateMetrics(level, metadata) {
        if (level === 'error') {
            const errorType = metadata.type || 'unknown';
            this.metrics.errorCounts[errorType] = (this.metrics.errorCounts[errorType] || 0) + 1;
        }
    }

    startAlertMonitoring() {
        // Check alerts every 60 seconds
        setInterval(() => {
            this.checkSystemAlerts();
        }, 60000);
    }

    checkAlerts(level, metadata) {
        // Check for consecutive failures
        if (this.consecutiveFailures >= this.alertThresholds.failureCount) {
            this.triggerAlert('HIGH_FAILURE_RATE', {
                consecutiveFailures: this.consecutiveFailures,
                threshold: this.alertThresholds.failureCount
            });
        }
    }

    checkSystemAlerts() {
        const memUsage = process.memoryUsage();
        const memoryUsageRatio = memUsage.heapUsed / memUsage.heapTotal;
        
        if (memoryUsageRatio > this.alertThresholds.memoryUsage) {
            this.triggerAlert('HIGH_MEMORY_USAGE', {
                currentUsage: Math.round(memoryUsageRatio * 100),
                threshold: Math.round(this.alertThresholds.memoryUsage * 100)
            });
        }

        // Check error rate
        const totalRequests = this.metrics.totalRequests;
        const totalErrors = Object.values(this.metrics.errorCounts).reduce((sum, count) => sum + count, 0);
        const errorRate = totalRequests > 0 ? totalErrors / totalRequests : 0;
        
        if (errorRate > this.alertThresholds.errorRate && totalRequests > 10) {
            this.triggerAlert('HIGH_ERROR_RATE', {
                errorRate: Math.round(errorRate * 100),
                threshold: Math.round(this.alertThresholds.errorRate * 100),
                totalRequests,
                totalErrors
            });
        }
    }

    triggerAlert(alertType, details) {
        this.warn('System alert triggered', {
            alertType,
            details,
            type: 'system_alert'
        });

        this.emit('alert', {
            type: alertType,
            details,
            timestamp: new Date().toISOString(),
            severity: this.getAlertSeverity(alertType)
        });
    }

    getAlertSeverity(alertType) {
        const severityMap = {
            'HIGH_FAILURE_RATE': 'HIGH',
            'HIGH_MEMORY_USAGE': 'MEDIUM',
            'HIGH_ERROR_RATE': 'HIGH',
            'SECURITY_VIOLATION': 'CRITICAL'
        };
        
        return severityMap[alertType] || 'MEDIUM';
    }

    getMetrics() {
        const uptime = Date.now() - this.metrics.startTime;
        const successRate = this.metrics.totalRequests > 0 
            ? (this.metrics.successfulScans / this.metrics.totalRequests) * 100 
            : 0;

        return {
            ...this.metrics,
            uptime,
            successRate: Math.round(successRate),
            averageResponseTime: this.calculateAverageResponseTime(),
            toolPerformance: this.getToolPerformanceMetrics()
        };
    }

    calculateAverageResponseTime() {
        if (this.metrics.performanceData.length === 0) return 0;
        
        const totalDuration = this.metrics.performanceData.reduce((sum, data) => sum + data.duration, 0);
        return Math.round(totalDuration / this.metrics.performanceData.length);
    }

    getToolPerformanceMetrics() {
        const performance = {};
        
        for (const [tool, stats] of Object.entries(this.metrics.toolExecutions)) {
            performance[tool] = {
                ...stats,
                successRate: stats.total > 0 ? Math.round((stats.successful / stats.total) * 100) : 0,
                averageDuration: stats.total > 0 ? Math.round(stats.totalDuration / stats.total) : 0
            };
        }
        
        return performance;
    }

    exportLogs(startDate, endDate, format = 'json') {
        // Implementation for exporting logs for analysis
        this.info('Log export requested', { startDate, endDate, format });
        // This would be implemented based on specific requirements
    }

    cleanup() {
        this.info('Logger cleanup initiated');
        this.removeAllListeners();
    }
}

module.exports = EnhancedLogger;
