#!/usr/bin/env node

// Test script to isolate the command line argument handling issue

console.log('<PERSON>rip<PERSON> started');

// Handle command line arguments FIRST, before any imports
const args = process.argv.slice(2);
console.log('Arguments:', args);

if (args.includes('--help') || args.includes('-h')) {
    console.log('Black-G CLI - AI Penetration Testing System\n');
    console.log('Usage:');
    console.log('  node black-g-cli.js [options]\n');
    console.log('Options:');
    console.log('  --help, -h     Show this help message');
    console.log('  --version, -v  Show version information');
    console.log('  --status       Show system status and exit\n');
    console.log('Examples:');
    console.log('  node black-g-cli.js              # Start interactive mode');
    console.log('  node black-g-cli.js --help       # Show this help');
    console.log('  node black-g-cli.js --status     # Check system status\n');
    console.log('For interactive mode, run without arguments.');
    process.exit(0);
}

if (args.includes('--version') || args.includes('-v')) {
    console.log('Black-G CLI v2.0.0');
    console.log('AI-Powered Attack Surface Management System');
    process.exit(0);
}

console.log('No help requested, would continue with full initialization...');
console.log('Exiting test script');
process.exit(0);
