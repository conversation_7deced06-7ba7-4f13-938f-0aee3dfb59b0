# 🔗 Black-G CLI Integration Guide

## 📋 Overview

This guide provides comprehensive instructions for integrating Black-G CLI with other security tools, CI/CD pipelines, enterprise systems, and monitoring platforms. The enhanced Black-G CLI v2.0 offers extensive integration capabilities for enterprise environments.

## 🛠️ API Integration

### REST API Wrapper

Create a REST API wrapper for Black-G CLI to enable programmatic access:

```javascript
// api-wrapper.js
const express = require('express');
const { spawn } = require('child_process');
const app = express();

app.use(express.json());

// Start a scan
app.post('/api/v1/scan', async (req, res) => {
    const { target, scanType, options } = req.body;
    
    try {
        const result = await executeBlackGScan(target, scanType, options);
        res.json({
            success: true,
            sessionId: result.sessionId,
            status: 'started'
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Get scan results
app.get('/api/v1/scan/:sessionId', async (req, res) => {
    const { sessionId } = req.params;
    
    try {
        const results = await getScanResults(sessionId);
        res.json({
            success: true,
            results: results
        });
    } catch (error) {
        res.status(404).json({
            success: false,
            error: 'Scan not found'
        });
    }
});

async function executeBlackGScan(target, scanType, options) {
    return new Promise((resolve, reject) => {
        const command = `echo "${scanType} scan on ${target}" | node black-g-cli.js --api-mode`;
        const child = spawn('bash', ['-c', command], {
            cwd: '/path/to/black-g',
            env: process.env
        });
        
        let output = '';
        child.stdout.on('data', (data) => {
            output += data.toString();
        });
        
        child.on('close', (code) => {
            if (code === 0) {
                const sessionId = extractSessionId(output);
                resolve({ sessionId, output });
            } else {
                reject(new Error(`Scan failed with code ${code}`));
            }
        });
    });
}

app.listen(3000, () => {
    console.log('Black-G API wrapper listening on port 3000');
});
```

### Python Integration

```python
# black_g_client.py
import subprocess
import json
import time
from typing import Dict, List, Optional

class BlackGClient:
    def __init__(self, black_g_path: str = '/path/to/black-g'):
        self.black_g_path = black_g_path
        
    def start_scan(self, target: str, scan_type: str = 'comprehensive') -> str:
        """Start a Black-G scan and return session ID"""
        command = f'echo "{scan_type} ASM analysis on {target}" | node black-g-cli.js --batch-mode'
        
        result = subprocess.run(
            command,
            shell=True,
            cwd=self.black_g_path,
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            return self._extract_session_id(result.stdout)
        else:
            raise Exception(f"Scan failed: {result.stderr}")
    
    def get_results(self, session_id: str) -> Dict:
        """Get scan results by session ID"""
        report_file = f"{self.black_g_path}/scan_reports/{session_id}_black-g-report.json"
        
        try:
            with open(report_file, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            raise Exception(f"Results not found for session {session_id}")
    
    def wait_for_completion(self, session_id: str, timeout: int = 3600) -> Dict:
        """Wait for scan completion and return results"""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                results = self.get_results(session_id)
                if results.get('status') == 'completed':
                    return results
            except Exception:
                pass
            
            time.sleep(30)  # Check every 30 seconds
        
        raise Exception(f"Scan timeout after {timeout} seconds")
    
    def _extract_session_id(self, output: str) -> str:
        """Extract session ID from Black-G output"""
        import re
        match = re.search(r'session:\s+([a-zA-Z0-9-]+)', output)
        if match:
            return match.group(1)
        raise Exception("Could not extract session ID")

# Usage example
if __name__ == "__main__":
    client = BlackGClient()
    
    # Start scan
    session_id = client.start_scan("example.com", "comprehensive")
    print(f"Started scan with session ID: {session_id}")
    
    # Wait for completion
    results = client.wait_for_completion(session_id)
    print(f"Scan completed with {len(results.get('findings', []))} findings")
```

## 🔄 CI/CD Pipeline Integration

### GitHub Actions

```yaml
# .github/workflows/security-scan.yml
name: Security Scan with Black-G CLI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  schedule:
    - cron: '0 2 * * 1'  # Weekly scan on Mondays at 2 AM

jobs:
  security-scan:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
    
    - name: Install security tools
      run: |
        sudo apt update
        sudo apt install -y nmap masscan sslscan gobuster
        
        # Install Go tools
        go install github.com/projectdiscovery/subfinder/v2/cmd/subfinder@latest
        go install github.com/projectdiscovery/nuclei/v3/cmd/nuclei@latest
        nuclei -update-templates
    
    - name: Setup Black-G CLI
      run: |
        git clone https://github.com/your-org/black-g-cli.git
        cd black-g-cli
        npm install
        chmod +x black-g-cli.js run-tests.sh
    
    - name: Run security tests
      env:
        GEMINI_API_KEY: ${{ secrets.GEMINI_API_KEY }}
        TARGET_DOMAIN: ${{ vars.TARGET_DOMAIN }}
      run: |
        cd black-g-cli
        echo "comprehensive ASM analysis on $TARGET_DOMAIN" | timeout 1800 node black-g-cli.js --ci-mode
    
    - name: Upload scan results
      uses: actions/upload-artifact@v3
      with:
        name: security-scan-results
        path: black-g-cli/scan_reports/
    
    - name: Parse results and create issues
      run: |
        cd black-g-cli
        python3 scripts/parse-results.py --create-issues --severity critical,high
```

### Jenkins Pipeline

```groovy
// Jenkinsfile
pipeline {
    agent any
    
    parameters {
        string(name: 'TARGET_DOMAIN', defaultValue: 'example.com', description: 'Target domain for security scan')
        choice(name: 'SCAN_TYPE', choices: ['passive', 'comprehensive', 'ssl-only'], description: 'Type of scan to perform')
    }
    
    environment {
        GEMINI_API_KEY = credentials('gemini-api-key')
        BLACK_G_PATH = '/opt/black-g-cli'
    }
    
    stages {
        stage('Preparation') {
            steps {
                script {
                    // Validate target domain
                    if (!params.TARGET_DOMAIN.matches(/^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/)) {
                        error("Invalid target domain format")
                    }
                }
            }
        }
        
        stage('Security Scan') {
            steps {
                dir("${BLACK_G_PATH}") {
                    script {
                        def scanCommand = "echo '${params.SCAN_TYPE} ASM analysis on ${params.TARGET_DOMAIN}' | timeout 3600 node black-g-cli.js --jenkins-mode"
                        
                        def result = sh(
                            script: scanCommand,
                            returnStatus: true
                        )
                        
                        if (result != 0) {
                            error("Security scan failed with exit code ${result}")
                        }
                    }
                }
            }
        }
        
        stage('Process Results') {
            steps {
                dir("${BLACK_G_PATH}") {
                    script {
                        // Archive scan reports
                        archiveArtifacts artifacts: 'scan_reports/*.json,scan_reports/*.md', fingerprint: true
                        
                        // Parse results for critical findings
                        def criticalFindings = sh(
                            script: "python3 scripts/count-critical-findings.py scan_reports/",
                            returnStdout: true
                        ).trim()
                        
                        if (criticalFindings.toInteger() > 0) {
                            currentBuild.result = 'UNSTABLE'
                            echo "WARNING: ${criticalFindings} critical security findings detected"
                        }
                    }
                }
            }
        }
        
        stage('Notification') {
            steps {
                script {
                    def scanResults = readJSON file: "${BLACK_G_PATH}/scan_reports/latest-report.json"
                    
                    emailext (
                        subject: "Security Scan Results for ${params.TARGET_DOMAIN}",
                        body: """
                        Security scan completed for ${params.TARGET_DOMAIN}
                        
                        Results Summary:
                        - Total Findings: ${scanResults.summary.total_findings}
                        - Critical: ${scanResults.summary.critical_findings}
                        - High: ${scanResults.summary.high_findings}
                        - Overall Risk: ${scanResults.summary.overall_risk}
                        
                        Full report attached.
                        """,
                        to: "${env.SECURITY_TEAM_EMAIL}",
                        attachmentsPattern: "${BLACK_G_PATH}/scan_reports/*.pdf"
                    )
                }
            }
        }
    }
    
    post {
        always {
            // Clean up temporary files
            dir("${BLACK_G_PATH}") {
                sh "find scan_reports/ -name '*.tmp' -delete"
            }
        }
        
        failure {
            // Send failure notification
            slackSend(
                channel: '#security-alerts',
                color: 'danger',
                message: "Security scan failed for ${params.TARGET_DOMAIN}. Check Jenkins logs for details."
            )
        }
    }
}
```

## 📊 Monitoring Integration

### Prometheus Metrics

```javascript
// prometheus-exporter.js
const express = require('express');
const client = require('prom-client');
const EnhancedLogger = require('./src/utils/enhanced-logger');

// Create metrics
const scanDuration = new client.Histogram({
    name: 'blackg_scan_duration_seconds',
    help: 'Duration of security scans',
    labelNames: ['target', 'scan_type', 'status']
});

const toolExecutions = new client.Counter({
    name: 'blackg_tool_executions_total',
    help: 'Total number of tool executions',
    labelNames: ['tool', 'status']
});

const findingsCounter = new client.Counter({
    name: 'blackg_findings_total',
    help: 'Total number of security findings',
    labelNames: ['severity', 'category']
});

// Set up metrics collection
const logger = new EnhancedLogger();

logger.on('log', (logEntry) => {
    if (logEntry.metadata.type === 'scan_complete') {
        scanDuration
            .labels(
                logEntry.metadata.target,
                logEntry.metadata.scanType || 'unknown',
                logEntry.metadata.success ? 'success' : 'failure'
            )
            .observe(logEntry.metadata.duration / 1000);
    }
    
    if (logEntry.metadata.type === 'tool_execution') {
        toolExecutions
            .labels(
                logEntry.metadata.tool,
                logEntry.metadata.success ? 'success' : 'failure'
            )
            .inc();
    }
});

// Expose metrics endpoint
const app = express();
app.get('/metrics', (req, res) => {
    res.set('Content-Type', client.register.contentType);
    res.end(client.register.metrics());
});

app.listen(9090, () => {
    console.log('Prometheus metrics server listening on port 9090');
});
```

### Grafana Dashboard

```json
{
  "dashboard": {
    "title": "Black-G CLI Security Monitoring",
    "panels": [
      {
        "title": "Scan Success Rate",
        "type": "stat",
        "targets": [
          {
            "expr": "rate(blackg_scan_duration_seconds_count{status=\"success\"}[5m]) / rate(blackg_scan_duration_seconds_count[5m]) * 100"
          }
        ]
      },
      {
        "title": "Average Scan Duration",
        "type": "stat",
        "targets": [
          {
            "expr": "rate(blackg_scan_duration_seconds_sum[5m]) / rate(blackg_scan_duration_seconds_count[5m])"
          }
        ]
      },
      {
        "title": "Security Findings by Severity",
        "type": "piechart",
        "targets": [
          {
            "expr": "sum by (severity) (blackg_findings_total)"
          }
        ]
      },
      {
        "title": "Tool Performance",
        "type": "table",
        "targets": [
          {
            "expr": "sum by (tool) (rate(blackg_tool_executions_total{status=\"success\"}[5m])) / sum by (tool) (rate(blackg_tool_executions_total[5m])) * 100"
          }
        ]
      }
    ]
  }
}
```

## 🔐 SIEM Integration

### Splunk Integration

```bash
# splunk-forwarder.conf
[monitor:///opt/black-g-cli/logs/]
disabled = false
index = security
sourcetype = blackg_logs

[monitor:///opt/black-g-cli/scan_reports/]
disabled = false
index = security
sourcetype = blackg_reports
```

```spl
# Splunk search queries for Black-G data

# Critical security findings
index=security sourcetype=blackg_reports 
| spath path=findings{} output=findings 
| mvexpand findings 
| spath input=findings path=severity output=severity 
| where severity="CRITICAL" 
| stats count by target, findings.type

# Tool failure analysis
index=security sourcetype=blackg_logs level=ERROR 
| spath path=metadata.tool output=tool 
| stats count by tool 
| sort -count

# Scan performance trends
index=security sourcetype=blackg_logs metadata.type=scan_complete 
| timechart avg(metadata.duration) by metadata.target
```

### ELK Stack Integration

```yaml
# logstash.conf
input {
  file {
    path => "/opt/black-g-cli/logs/*.log"
    start_position => "beginning"
    codec => "json"
    tags => ["blackg", "security"]
  }
}

filter {
  if "blackg" in [tags] {
    mutate {
      add_field => { "source_system" => "black-g-cli" }
    }
    
    if [metadata][type] == "security_event" {
      mutate {
        add_tag => ["security_alert"]
      }
    }
  }
}

output {
  elasticsearch {
    hosts => ["localhost:9200"]
    index => "security-logs-%{+YYYY.MM.dd}"
  }
}
```

## 🏢 Enterprise Integration

### Active Directory Integration

```javascript
// ad-integration.js
const ActiveDirectory = require('activedirectory');

class ADIntegration {
    constructor(config) {
        this.ad = new ActiveDirectory(config);
    }
    
    async authorizeUser(username, groups) {
        return new Promise((resolve, reject) => {
            this.ad.isUserMemberOf(username, groups, (err, isMember) => {
                if (err) reject(err);
                else resolve(isMember);
            });
        });
    }
    
    async getSecurityTeamMembers() {
        return new Promise((resolve, reject) => {
            this.ad.getUsersForGroup('Security-Team', (err, users) => {
                if (err) reject(err);
                else resolve(users);
            });
        });
    }
}

// Usage in Black-G CLI
const adConfig = {
    url: 'ldap://dc.company.com',
    baseDN: 'dc=company,dc=com',
    username: '<EMAIL>',
    password: process.env.AD_PASSWORD
};

const ad = new ADIntegration(adConfig);

// Check authorization before allowing scans
async function checkScanAuthorization(username, targetDomain) {
    const authorizedGroups = ['Security-Team', 'Pentest-Team'];
    const isAuthorized = await ad.authorizeUser(username, authorizedGroups);
    
    if (!isAuthorized) {
        throw new Error('User not authorized for security scanning');
    }
    
    return true;
}
```

### ServiceNow Integration

```javascript
// servicenow-integration.js
const axios = require('axios');

class ServiceNowIntegration {
    constructor(instance, username, password) {
        this.baseURL = `https://${instance}.service-now.com/api/now/table`;
        this.auth = {
            username: username,
            password: password
        };
    }
    
    async createSecurityIncident(finding) {
        const incident = {
            short_description: `Security Finding: ${finding.type}`,
            description: `
                Target: ${finding.target}
                Severity: ${finding.severity}
                Category: ${finding.category}
                Details: ${finding.description}
                
                Discovered by Black-G CLI automated security scan.
            `,
            urgency: this.mapSeverityToUrgency(finding.severity),
            impact: this.mapSeverityToImpact(finding.severity),
            category: 'Security',
            subcategory: 'Vulnerability'
        };
        
        try {
            const response = await axios.post(
                `${this.baseURL}/incident`,
                incident,
                { auth: this.auth }
            );
            
            return response.data.result.number;
        } catch (error) {
            throw new Error(`Failed to create ServiceNow incident: ${error.message}`);
        }
    }
    
    mapSeverityToUrgency(severity) {
        const mapping = {
            'CRITICAL': '1',
            'HIGH': '2',
            'MEDIUM': '3',
            'LOW': '4'
        };
        return mapping[severity] || '4';
    }
    
    mapSeverityToImpact(severity) {
        const mapping = {
            'CRITICAL': '1',
            'HIGH': '2',
            'MEDIUM': '3',
            'LOW': '4'
        };
        return mapping[severity] || '4';
    }
}

// Auto-create incidents for critical findings
async function processFindings(scanResults) {
    const serviceNow = new ServiceNowIntegration(
        process.env.SERVICENOW_INSTANCE,
        process.env.SERVICENOW_USERNAME,
        process.env.SERVICENOW_PASSWORD
    );
    
    const criticalFindings = scanResults.findings.filter(f => f.severity === 'CRITICAL');
    
    for (const finding of criticalFindings) {
        try {
            const incidentNumber = await serviceNow.createSecurityIncident(finding);
            console.log(`Created ServiceNow incident: ${incidentNumber}`);
        } catch (error) {
            console.error(`Failed to create incident for finding: ${error.message}`);
        }
    }
}
```

---

**🎯 Summary**: The enhanced Black-G CLI v2.0 provides extensive integration capabilities for enterprise environments, enabling seamless integration with CI/CD pipelines, monitoring systems, SIEM platforms, and enterprise tools. These integrations ensure that security scanning becomes an integral part of your organization's security operations and development lifecycle.
