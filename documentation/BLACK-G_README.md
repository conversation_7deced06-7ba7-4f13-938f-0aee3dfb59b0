# 🤖 Black-G CLI - AI-Driven Penetration Testing System

**Black-G CLI** is a specialized command-line interface for Attack Surface Management (ASM) and penetration testing on Parrot OS Security Edition. It combines natural language processing with intelligent tool selection to provide a seamless, AI-driven security testing experience.

## 🎯 Key Features

### 🧠 **Natural Language Interface**
- **Conversational Commands**: Describe your security testing objectives in plain English
- **Context Awareness**: Maintains conversation context for complex multi-step assessments
- **Intent Recognition**: Automatically understands and categorizes security testing requests

### 🔧 **Intelligent Tool Selection**
- **Automated Tool Selection**: AI selects the best tools based on your objectives
- **Parrot OS Integration**: Seamlessly integrates with pre-installed security tools
- **Execution Planning**: Creates optimized execution sequences for maximum effectiveness

### 📊 **Comprehensive ASM Analysis**
Black-G covers all 7 core Attack Surface Management categories:

1. **Domain/IP Vulnerabilities** - Network and application vulnerability assessment
2. **SSL/TLS Certificate Analysis** - Certificate security and configuration analysis
3. **Configuration Issues** - System and service misconfiguration detection
4. **Open Ports & Services** - Network service enumeration and analysis
5. **IP/Domain Reputation** - Threat intelligence and reputation analysis
6. **Cloud Security Assessment** - Cloud-specific vulnerability detection
7. **Authentication Discovery** - Authentication mechanism analysis

### 📋 **Professional Reporting**
- **Multiple Formats**: JSON, Markdown, and DOCX reports
- **AI-Generated Analysis**: Intelligent insights and recommendations
- **Risk Assessment**: Automated risk scoring and prioritization
- **Executive Summaries**: Professional reports suitable for stakeholders

### 🌐 **Remote Access Compatible**
- **PuTTY/SSH Optimized**: Perfect for remote penetration testing
- **Terminal-Based**: No GUI dependencies, works in any terminal environment
- **ANSI Color Support**: Enhanced readability with color-coded output

## 🚀 Quick Start

### Installation & Setup

1. **Ensure Prerequisites**
   ```bash
   # Black-G CLI is already integrated into your existing system
   cd /path/to/gemini-black
   
   # Verify your .env file contains GEMINI_API_KEY
   cat .env
   ```

2. **Launch Black-G CLI**
   ```bash
   # Using the integrated startup script
   ./start_interactive_asm.sh black-g
   
   # Or directly
   node black-g-cli.js
   ```

3. **Start Testing**
   ```
   Black-G> Perform passive reconnaissance on example.com
   Black-G> Run comprehensive ASM analysis on my web application
   Black-G> Check SSL vulnerabilities on target.company.com
   ```

## 💬 Usage Examples

### **Passive Reconnaissance**
```
Black-G> "I need to perform passive reconnaissance on example.com"

🎯 OBJECTIVE: Passive reconnaissance on example.com
📊 STRATEGY: Subdomain enumeration → DNS analysis → OSINT gathering
🔍 EXECUTION PLAN: 
   1. Subdomain discovery using subfinder
   2. Asset mapping with amass
   3. DNS record analysis
⚠️ RISK_ASSESSMENT: Low risk - no direct target interaction
📋 EXPECTED_OUTCOMES: Subdomain list, DNS records, asset inventory

🔧 Detected 3 command(s) for execution:
1. subfinder -d example.com -o subdomains.txt
2. amass enum -d example.com -o amass_results.txt
3. dig example.com ANY

❓ Execute these commands? (y/n): y
```

### **Comprehensive ASM Analysis**
```
Black-G> "Run full ASM analysis on webapp.company.com"

🎯 OBJECTIVE: Comprehensive Attack Surface Management analysis
📊 STRATEGY: Multi-phase analysis covering all 7 ASM categories
🔍 EXECUTION PLAN:
   Phase 1: Asset Discovery (subdomains, DNS)
   Phase 2: Port Scanning (nmap service detection)
   Phase 3: Vulnerability Assessment (nuclei templates)
   Phase 4: SSL/TLS Analysis (certificate validation)
   Phase 5: Configuration Review (security headers, etc.)

🔧 Detected 6 command(s) for execution:
1. subfinder -d webapp.company.com
2. nmap -sS -sV webapp.company.com
3. nuclei -target https://webapp.company.com
4. sslscan webapp.company.com
5. nmap --script ssl-enum-ciphers webapp.company.com
6. curl -I https://webapp.company.com

❓ Execute these commands? (y/n): y
```

### **Targeted SSL Analysis**
```
Black-G> "Check SSL vulnerabilities on my web servers"

🎯 OBJECTIVE: SSL/TLS security assessment
📊 STRATEGY: Certificate analysis and vulnerability detection
🔍 EXECUTION PLAN:
   1. SSL certificate analysis
   2. Cipher suite evaluation
   3. Protocol version testing
   4. Vulnerability scanning (Heartbleed, etc.)

🔧 Detected 3 command(s) for execution:
1. sslscan target.com
2. nmap --script ssl-* target.com
3. nuclei -tags ssl -target https://target.com

❓ Execute these commands? (y/n): y
```

## 🎮 Built-in Commands

| Command | Description |
|---------|-------------|
| `help` | Show comprehensive help and usage examples |
| `status` | Display current system status and active scans |
| `history` | Show command history for current session |
| `clear` | Clear screen and redisplay banner |
| `exit` | Exit Black-G CLI |

## 🔧 Tool Integration

Black-G CLI integrates with the following Parrot OS security tools:

### **Network Analysis**
- **nmap**: Port scanning, service detection, OS fingerprinting
- **masscan**: High-speed port scanning for large networks

### **Asset Discovery**
- **subfinder**: Fast subdomain discovery
- **amass**: Comprehensive attack surface mapping

### **Vulnerability Assessment**
- **nuclei**: Template-based vulnerability scanning
- **Custom scripts**: Specialized vulnerability checks

### **SSL/TLS Analysis**
- **sslscan**: SSL/TLS configuration analysis
- **testssl**: Comprehensive SSL/TLS testing

### **Web Application Testing**
- **gobuster**: Directory and file brute forcing
- **Custom web analysis tools**

## 📊 ASM Categories Explained

### 1. **Domain/IP Vulnerabilities**
- Network service vulnerabilities
- Application-level security issues
- Protocol-specific weaknesses
- Service misconfigurations

### 2. **SSL/TLS Certificate Analysis**
- Certificate validity and expiration
- Cipher suite strength
- Protocol version support
- Certificate chain validation

### 3. **Configuration Issues**
- Security header analysis
- Service configuration review
- Access control misconfigurations
- Default credential detection

### 4. **Open Ports & Services**
- Port scanning and enumeration
- Service version detection
- Banner grabbing
- Service-specific testing

### 5. **IP/Domain Reputation**
- Threat intelligence integration
- Blacklist checking
- Historical compromise data
- Reputation scoring

### 6. **Cloud Security Assessment**
- Cloud service enumeration
- Misconfigured cloud resources
- Access control issues
- Cloud-specific vulnerabilities

### 7. **Authentication Discovery**
- Login page identification
- Authentication mechanism analysis
- Multi-factor authentication detection
- Password policy assessment

## 🛡️ Security & Ethics

### **Ethical Usage Guidelines**
- **Authorization Required**: Only scan systems you own or have explicit permission to test
- **Responsible Disclosure**: Follow responsible disclosure practices for discovered vulnerabilities
- **Rate Limiting**: Respect target systems and avoid aggressive scanning
- **Legal Compliance**: Ensure compliance with local laws and regulations

### **Built-in Safety Features**
- **Confirmation Required**: All commands require user approval before execution
- **Scan Logging**: Complete audit trail of all executed commands
- **Rate Limiting**: Built-in delays to avoid overwhelming target systems
- **Error Handling**: Graceful handling of failed commands and network issues

## 🔌 Remote Access (PuTTY/SSH)

Black-G CLI is optimized for remote access scenarios:

### **SSH Connection**
```bash
# Connect to your Parrot OS system
ssh user@parrot-system

# Navigate to Black-G directory
cd /path/to/gemini-black

# Launch Black-G CLI
./start_interactive_asm.sh black-g
```

### **PuTTY Configuration**
- **Terminal Type**: xterm-256color (for full color support)
- **Character Set**: UTF-8
- **Scrollback**: 10000+ lines recommended
- **Colors**: Enable ANSI colors for optimal experience

## 📈 Reporting Features

### **Automatic Report Generation**
After each scan session, Black-G offers to generate professional reports:

- **JSON Reports**: Machine-readable results for integration
- **Markdown Reports**: Human-readable technical documentation
- **DOCX Reports**: Professional reports using existing templates

### **AI-Generated Analysis**
Each report includes:
- Executive summary of findings
- Risk assessment and scoring
- Detailed technical analysis
- Remediation recommendations
- Next steps and follow-up actions

## 🔧 Troubleshooting

### **Common Issues**

**Black-G won't start**
```bash
# Check Node.js installation
node --version

# Verify dependencies
npm install

# Check API key
echo $GEMINI_API_KEY
```

**Tools not found**
```bash
# Install missing tools (Parrot OS)
sudo apt update
sudo apt install nmap subfinder nuclei

# Verify tool paths
which nmap subfinder nuclei
```

**Permission errors**
```bash
# Some tools may require sudo for certain scans
# Black-G will prompt when elevated privileges are needed
```

## 🆘 Support

- **Built-in Help**: Use the `help` command within Black-G CLI
- **System Status**: Use `status` command to check system health
- **Logs**: Check `./logs/` directory for detailed error logs
- **Session History**: Use `history` command to review past commands

---

**Black-G CLI v1.0.0**  
*AI-Driven Penetration Testing for Parrot OS*  
*© 2025 - Advanced Attack Surface Management*
