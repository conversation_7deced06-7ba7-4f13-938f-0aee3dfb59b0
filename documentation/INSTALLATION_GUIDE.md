# 🚀 Black-G CLI Installation & Setup Guide

## 📋 Overview

Black-G CLI is an advanced AI-driven penetration testing system specialized in Attack Surface Management (ASM) for Parrot OS Security Edition. This guide will walk you through the complete installation, configuration, and usage process.

## 🎯 Prerequisites

### System Requirements
- **Operating System**: Parrot OS Security Edition (recommended) or any Debian-based Linux
- **Node.js**: Version 18.0 or higher
- **Memory**: Minimum 4GB RAM (8GB recommended)
- **Storage**: 2GB free space for tools and reports
- **Network**: Internet connection for tool updates and AI API access

### Required Accounts
- **Google AI Studio Account**: For Gemini API access
- **GitHub Account**: For tool installations (optional but recommended)

## 🛠️ Installation Process

### Step 1: System Preparation

```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Install essential dependencies
sudo apt install -y curl wget git build-essential

# Install Node.js (if not already installed)
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt-get install -y nodejs

# Verify Node.js installation
node --version
npm --version
```

### Step 2: Install Security Tools

#### Core System Tools
```bash
# Install essential penetration testing tools
sudo apt install -y nmap masscan sslscan gobuster whois dnsutils

# Verify installations
which nmap masscan sslscan gobuster
```

#### Go-based Tools
```bash
# Install Go (if not already installed)
sudo apt install -y golang-go

# Set up Go environment
echo 'export GOPATH=$HOME/go' >> ~/.bashrc
echo 'export PATH=$PATH:$GOPATH/bin' >> ~/.bashrc
source ~/.bashrc

# Install Go-based security tools
go install -v github.com/projectdiscovery/subfinder/v2/cmd/subfinder@latest
go install -v github.com/owasp-amass/amass/v4/...@master
go install -v github.com/projectdiscovery/nuclei/v3/cmd/nuclei@latest

# Update Nuclei templates
nuclei -update-templates
```

#### Additional Tools (Optional)
```bash
# Install testssl.sh for advanced SSL testing
sudo git clone https://github.com/drwetter/testssl.sh.git /opt/testssl
sudo ln -s /opt/testssl/testssl.sh /usr/local/bin/testssl
sudo chmod +x /usr/local/bin/testssl
```

### Step 3: Clone and Setup Black-G CLI

```bash
# Navigate to your preferred directory
cd ~

# Clone the repository (assuming you have the code)
# git clone https://github.com/your-repo/gemini-black.git
# cd gemini-black

# Or if you already have the code:
cd /path/to/gemini-black

# Install Node.js dependencies
npm install

# Make scripts executable
chmod +x start_interactive_asm.sh
chmod +x run-tests.sh
chmod +x black-g-cli.js
```

### Step 4: Configure Environment

#### Create .env File
```bash
# Create environment configuration
cp .env.example .env  # If example exists, or create new file
nano .env
```

Add the following content to `.env`:
```env
# Google Gemini AI Configuration
GEMINI_API_KEY=your_gemini_api_key_here

# Black-G CLI Configuration
BLACK_G_LOG_LEVEL=info
BLACK_G_MAX_RETRIES=3
BLACK_G_TIMEOUT=30000

# Report Configuration
REPORT_OUTPUT_DIR=./scan_reports
REPORT_TEMPLATE_DIR=./report template

# Security Configuration
ENABLE_COMMAND_VALIDATION=true
ENABLE_RISK_ASSESSMENT=true
MAX_CONCURRENT_SCANS=3
```

#### Get Gemini API Key
1. Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Sign in with your Google account
3. Create a new API key
4. Copy the key and paste it in your `.env` file

### Step 5: Verify Installation

```bash
# Run the comprehensive test suite
./run-tests.sh

# Expected output should show:
# - Tool discovery: 10/10 tools available
# - Test success rate: 90%+ 
# - Overall status: PASS
```

## 🔧 Configuration Options

### Tool Configuration

#### Custom Tool Paths
If tools are installed in non-standard locations, you can specify custom paths:

```bash
# Create custom tool configuration
nano config/tools.json
```

```json
{
  "customPaths": {
    "nmap": "/custom/path/to/nmap",
    "subfinder": "/custom/path/to/subfinder"
  },
  "searchPaths": [
    "/usr/bin",
    "/usr/local/bin",
    "/home/<USER>/go/bin",
    "/custom/tools/bin"
  ]
}
```

#### Tool-specific Settings
```bash
# Configure Nuclei
nuclei -update-templates
nuclei -update

# Configure Amass
mkdir -p ~/.config/amass
cat > ~/.config/amass/config.ini << EOF
[scope]
port = 80,443,8080,8443

[bruteforce]
enabled = true
recursive = true
EOF
```

### AI Configuration

#### Advanced AI Settings
```env
# In .env file - Advanced AI configuration
GEMINI_MODEL=gemini-2.5-flash
GEMINI_TEMPERATURE=0.3
GEMINI_MAX_TOKENS=2048
GEMINI_TOP_K=40
GEMINI_TOP_P=0.8

# Context management
AI_CONTEXT_HISTORY_LIMIT=10
AI_CONTEXT_INCLUDE_TOOLS=true
AI_CONTEXT_INCLUDE_FINDINGS=true
```

### Security Configuration

#### Command Validation Settings
```env
# Security settings in .env
ENABLE_COMMAND_INJECTION_PROTECTION=true
ENABLE_PRIVILEGE_ESCALATION_PROTECTION=true
ALLOWED_COMMAND_PATTERNS="nmap,subfinder,nuclei,sslscan,gobuster,amass"
BLOCKED_COMMAND_PATTERNS="rm,dd,mkfs,format,del"
```

## 🚀 Usage Guide

### Basic Usage

#### Starting Black-G CLI
```bash
# Start the interactive CLI
./start_interactive_asm.sh black-g

# Or run directly
node black-g-cli.js
```

#### First Scan Example
```
Black-G> I want to perform a comprehensive ASM assessment on example.com

# The system will:
# 1. Analyze your request using AI
# 2. Generate an execution plan
# 3. Show you the commands to be executed
# 4. Ask for confirmation
# 5. Execute the approved commands
# 6. Generate a comprehensive report
```

### Advanced Usage

#### Passive Reconnaissance
```
Black-G> Perform passive reconnaissance on target.company.com without direct interaction

# Expected tools: subfinder, amass, whois, dig
# Risk level: LOW
# No direct target interaction
```

#### SSL/TLS Analysis
```
Black-G> Check SSL vulnerabilities and certificate issues on secure.example.com

# Expected tools: sslscan, testssl, nmap with SSL scripts
# Risk level: MEDIUM
# Focuses on certificate and encryption analysis
```

#### Comprehensive ASM Scan
```
Black-G> Run full attack surface management analysis on webapp.company.com

# Expected tools: All available tools across 7 ASM categories
# Risk level: HIGH (includes active scanning)
# Comprehensive analysis with detailed reporting
```

### Built-in Commands

| Command | Description |
|---------|-------------|
| `help` | Show comprehensive help and usage examples |
| `status` | Display current system status and tool availability |
| `history` | Show command history for current session |
| `clear` | Clear screen and redisplay banner |
| `exit` | Exit Black-G CLI |

## 📊 Report Generation

### Automatic Reports
After each scan session, Black-G offers to generate professional reports:

```
❓ Generate professional report? (y/n): y
[+] Generating professional report...
[✓] JSON report saved: scan_reports/2025-07-12_report.json
[✓] Markdown report saved: scan_reports/2025-07-12_report.md
[✓] DOCX report saved: scan_reports/2025-07-12_report.docx
```

### Report Types

#### JSON Reports
- Machine-readable format
- Complete scan data
- API integration friendly
- Automated processing

#### Markdown Reports
- Human-readable format
- Technical documentation
- Version control friendly
- Easy sharing and collaboration

#### DOCX Reports
- Professional presentation format
- Executive summaries
- Client deliverables
- Compliance documentation

## 🔍 Troubleshooting

### Common Issues

#### Tool Not Found Errors
```bash
# Check tool availability
which nmap subfinder nuclei

# Verify PATH environment
echo $PATH

# Reinstall missing tools
sudo apt install nmap
go install github.com/projectdiscovery/subfinder/v2/cmd/subfinder@latest
```

#### Permission Errors
```bash
# Fix script permissions
chmod +x start_interactive_asm.sh black-g-cli.js run-tests.sh

# Fix directory permissions
chmod 755 scan_reports test
```

#### API Key Issues
```bash
# Verify API key is set
grep GEMINI_API_KEY .env

# Test API connectivity
curl -H "Authorization: Bearer $GEMINI_API_KEY" \
     https://generativelanguage.googleapis.com/v1/models
```

#### Memory/Performance Issues
```bash
# Check system resources
free -h
df -h

# Optimize Node.js memory
export NODE_OPTIONS="--max-old-space-size=4096"
```

### Debug Mode
```bash
# Enable debug logging
export BLACK_G_LOG_LEVEL=debug
node black-g-cli.js

# Check logs
tail -f logs/error.log
```

## 🔒 Security Considerations

### Ethical Usage
- **Authorization Required**: Only scan systems you own or have explicit permission to test
- **Rate Limiting**: Respect target systems and avoid aggressive scanning
- **Legal Compliance**: Ensure compliance with local laws and regulations
- **Responsible Disclosure**: Follow responsible disclosure practices

### System Security
- **API Key Protection**: Keep your Gemini API key secure and never commit it to version control
- **Network Security**: Use VPN or isolated networks for sensitive testing
- **Data Protection**: Secure scan results and reports containing sensitive information

## 📞 Support and Resources

### Documentation
- **User Guide**: Complete usage instructions with examples
- **API Documentation**: Integration guides for developers
- **Troubleshooting Guide**: Common issues and solutions

### Community
- **GitHub Issues**: Bug reports and feature requests
- **Security Community**: Best practices and knowledge sharing
- **Training Resources**: Video tutorials and workshops

### Updates
```bash
# Update Black-G CLI
git pull origin main
npm install

# Update security tools
sudo apt update && sudo apt upgrade
nuclei -update-templates
```

---

**🎉 Congratulations!** You now have a fully configured Black-G CLI system ready for professional Attack Surface Management assessments.
