#!/bin/bash

# Black-G CLI Testing Script
# Comprehensive testing for the Black-G CLI system

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo -e "${CYAN}🧪 Black-G CLI Testing Framework${NC}"
echo -e "${CYAN}=================================${NC}\n"

# Check if Node.js is available
if ! command -v node &> /dev/null; then
    echo -e "${RED}❌ Node.js is not installed or not in PATH${NC}"
    exit 1
fi

# Check if test directory exists
if [ ! -d "test" ]; then
    echo -e "${RED}❌ Test directory not found${NC}"
    exit 1
fi

# Run pre-test checks
echo -e "${YELLOW}🔍 Running pre-test checks...${NC}"

# Check if main Black-G CLI file exists
if [ ! -f "black-g-cli.js" ]; then
    echo -e "${RED}❌ black-g-cli.js not found${NC}"
    exit 1
fi

# Check if essential tools are available
echo -e "${CYAN}📋 Checking tool availability...${NC}"

tools=("nmap" "node" "npm")
missing_tools=()

for tool in "${tools[@]}"; do
    if command -v "$tool" &> /dev/null; then
        echo -e "  ${GREEN}✓${NC} $tool"
    else
        echo -e "  ${RED}✗${NC} $tool (missing)"
        missing_tools+=("$tool")
    fi
done

if [ ${#missing_tools[@]} -gt 0 ]; then
    echo -e "\n${YELLOW}⚠️  Some tools are missing but tests will continue${NC}"
    echo -e "${YELLOW}   Missing: ${missing_tools[*]}${NC}"
fi

# Run the main test suite
echo -e "\n${CYAN}🚀 Running Black-G CLI tests...${NC}\n"

# Make test file executable
chmod +x test/black-g-tests.js

# Run the tests
if node test/black-g-tests.js; then
    echo -e "\n${GREEN}✅ All tests completed successfully!${NC}"
    exit_code=0
else
    echo -e "\n${RED}❌ Some tests failed!${NC}"
    exit_code=1
fi

# Additional integration tests
echo -e "\n${CYAN}🔧 Running integration tests...${NC}"

# Test 1: Check if Black-G CLI can start without errors
echo -e "${YELLOW}Testing Black-G CLI startup...${NC}"
if timeout 10s node black-g-cli.js --help >/dev/null 2>&1; then
    echo -e "  ${GREEN}✓${NC} Black-G CLI startup test passed"
elif [ $? -eq 124 ]; then
    echo -e "  ${RED}✗${NC} Black-G CLI startup test failed (timeout)"
    exit_code=1
else
    echo -e "  ${RED}✗${NC} Black-G CLI startup test failed"
    exit_code=1
fi

# Test 1.1: Check version command
echo -e "${YELLOW}Testing Black-G CLI version command...${NC}"
if timeout 5s node black-g-cli.js --version >/dev/null 2>&1; then
    echo -e "  ${GREEN}✓${NC} Black-G CLI version test passed"
else
    echo -e "  ${RED}✗${NC} Black-G CLI version test failed"
    exit_code=1
fi

# Test 1.2: Check status command
echo -e "${YELLOW}Testing Black-G CLI status command...${NC}"
if timeout 15s node black-g-cli.js --status >/dev/null 2>&1; then
    echo -e "  ${GREEN}✓${NC} Black-G CLI status test passed"
else
    echo -e "  ${RED}✗${NC} Black-G CLI status test failed"
    exit_code=1
fi

# Test 2: Check if environment variables are properly loaded
echo -e "${YELLOW}Testing environment configuration...${NC}"
if [ -f ".env" ]; then
    if grep -q "GEMINI_API_KEY" .env; then
        echo -e "  ${GREEN}✓${NC} Environment configuration found"
    else
        echo -e "  ${YELLOW}⚠️${NC} GEMINI_API_KEY not found in .env"
    fi
else
    echo -e "  ${YELLOW}⚠️${NC} .env file not found"
fi

# Test 3: Check if report templates exist
echo -e "${YELLOW}Testing report template availability...${NC}"
if [ -d "report template" ]; then
    echo -e "  ${GREEN}✓${NC} Report template directory found"
else
    echo -e "  ${YELLOW}⚠️${NC} Report template directory not found"
fi

# Test 4: Check if scan reports directory is writable
echo -e "${YELLOW}Testing scan reports directory...${NC}"
if [ -d "scan_reports" ] || mkdir -p scan_reports 2>/dev/null; then
    if [ -w "scan_reports" ]; then
        echo -e "  ${GREEN}✓${NC} Scan reports directory is writable"
    else
        echo -e "  ${RED}✗${NC} Scan reports directory is not writable"
        exit_code=1
    fi
else
    echo -e "  ${RED}✗${NC} Cannot create scan reports directory"
    exit_code=1
fi

# Performance test
echo -e "\n${CYAN}⚡ Running performance tests...${NC}"

# Test tool discovery performance
echo -e "${YELLOW}Testing tool discovery performance...${NC}"
start_time=$(date +%s%N)
which nmap subfinder nuclei sslscan gobuster amass 2>/dev/null || true
end_time=$(date +%s%N)
duration=$(( (end_time - start_time) / 1000000 )) # Convert to milliseconds

if [ $duration -lt 1000 ]; then
    echo -e "  ${GREEN}✓${NC} Tool discovery completed in ${duration}ms (excellent)"
elif [ $duration -lt 3000 ]; then
    echo -e "  ${GREEN}✓${NC} Tool discovery completed in ${duration}ms (good)"
else
    echo -e "  ${YELLOW}⚠️${NC} Tool discovery took ${duration}ms (slow)"
fi

# Security tests
echo -e "\n${CYAN}🔒 Running security tests...${NC}"

# Test 1: Check for potential security issues in code
echo -e "${YELLOW}Testing for security issues...${NC}"
if grep -r "eval\|exec\|system" black-g-cli.js test/ 2>/dev/null | grep -v "// Safe:" | grep -v "test" >/dev/null; then
    echo -e "  ${RED}✗${NC} Potential security issues found (eval/exec/system usage)"
    exit_code=1
else
    echo -e "  ${GREEN}✓${NC} No obvious security issues detected"
fi

# Test 2: Check file permissions
echo -e "${YELLOW}Testing file permissions...${NC}"
if [ -x "black-g-cli.js" ]; then
    echo -e "  ${GREEN}✓${NC} Main CLI file has execute permissions"
else
    echo -e "  ${YELLOW}⚠️${NC} Main CLI file is not executable"
fi

# Generate test report
echo -e "\n${CYAN}📊 Generating test report...${NC}"

report_file="test-report-$(date +%Y%m%d-%H%M%S).txt"
{
    echo "Black-G CLI Test Report"
    echo "======================"
    echo "Date: $(date)"
    echo "System: $(uname -a)"
    echo "Node.js: $(node --version 2>/dev/null || echo 'Not available')"
    echo ""
    echo "Tool Availability:"
    for tool in nmap subfinder nuclei sslscan gobuster amass; do
        if command -v "$tool" &> /dev/null; then
            echo "  ✓ $tool: $(which $tool)"
        else
            echo "  ✗ $tool: Not found"
        fi
    done
    echo ""
    echo "Test Results: See console output above"
    echo "Exit Code: $exit_code"
} > "$report_file"

echo -e "  ${GREEN}✓${NC} Test report saved to: $report_file"

# Final summary
echo -e "\n${CYAN}📋 Test Summary${NC}"
echo -e "${CYAN}===============${NC}"

if [ $exit_code -eq 0 ]; then
    echo -e "${GREEN}🎉 All tests passed successfully!${NC}"
    echo -e "${GREEN}   Black-G CLI is ready for use.${NC}"
else
    echo -e "${RED}❌ Some tests failed.${NC}"
    echo -e "${RED}   Please review the issues above before using Black-G CLI.${NC}"
fi

echo -e "\n${CYAN}📖 Next Steps:${NC}"
echo -e "   1. Review any warnings or failed tests"
echo -e "   2. Install missing tools if needed"
echo -e "   3. Configure .env file with GEMINI_API_KEY"
echo -e "   4. Run: ./start_interactive_asm.sh black-g"

exit $exit_code
