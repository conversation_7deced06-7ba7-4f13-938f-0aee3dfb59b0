#!/usr/bin/env node

/**
 * Black-G CLI - Interactive AI-Driven Penetration Testing System
 * Specialized CLI for Attack Surface Management on Parrot OS
 *
 * Features:
 * - Natural language processing for security testing requests
 * - Intelligent tool selection and execution
 * - Comprehensive ASM analysis across 7 core categories
 * - Professional report generation
 * - PuTTY/SSH compatible interface
 */

// Handle command line arguments FIRST, before any imports that might hang
if (require.main === module) {
    const args = process.argv.slice(2);

    if (args.includes('--help') || args.includes('-h')) {
        console.log('Black-G CLI - AI Penetration Testing System\n');
        console.log('Usage:');
        console.log('  node black-g-cli.js [options]\n');
        console.log('Options:');
        console.log('  --help, -h     Show this help message');
        console.log('  --version, -v  Show version information');
        console.log('  --status       Show system status and exit\n');
        console.log('Examples:');
        console.log('  node black-g-cli.js              # Start interactive mode');
        console.log('  node black-g-cli.js --help       # Show this help');
        console.log('  node black-g-cli.js --status     # Check system status\n');
        console.log('For interactive mode, run without arguments.');
        process.exit(0);
    }

    if (args.includes('--version') || args.includes('-v')) {
        console.log('Black-G CLI v2.0.0');
        console.log('AI-Powered Attack Surface Management System');
        process.exit(0);
    }

    if (args.includes('--status')) {
        console.log('Black-G CLI System Status\n');
        console.log('✓ Black-G CLI is ready');
        console.log('✓ Node.js runtime available');
        console.log('✓ Environment configuration loaded');

        // Check if .env file exists
        const fs = require('fs');
        if (fs.existsSync('.env')) {
            console.log('✓ Environment file found');
        } else {
            console.log('⚠ Environment file not found');
        }

        console.log('\nFor detailed tool status, run the interactive mode.');
        process.exit(0);
    }
}

const readline = require('readline');
const { GoogleGenerativeAI } = require('@google/generative-ai');
const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');
require('dotenv').config();

// Import existing services and utilities
const { config } = require('./src/config');
const logger = require('./src/utils/logger');
const EnhancedLogger = require('./src/utils/enhanced-logger');

// ANSI Colors for terminal output (PuTTY compatible)
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m',
    white: '\x1b[37m'
};

class BlackGCLI {
    constructor() {
        this.genAI = null;
        this.model = null;
        this.chatHistory = [];
        this.currentSession = null;
        this.activeScan = false;
        this.scanResults = {};

        // Initialize enhanced logging
        this.enhancedLogger = new EnhancedLogger({
            logLevel: process.env.BLACK_G_LOG_LEVEL || 'info',
            enableMetrics: true,
            enableAlerts: true
        });

        // Set up event listeners for monitoring
        this.setupMonitoring();

        // Initialize components
        this.toolSelector = new ToolSelector();
        this.asmAnalyzer = new ASMAnalyzer();
        this.reporter = new BlackGReporter();

        this.initializeSystem();
    }

    setupMonitoring() {
        // Set up alert handlers
        this.enhancedLogger.on('alert', (alert) => {
            this.handleSystemAlert(alert);
        });

        // Set up security event handlers
        this.enhancedLogger.on('security_alert', (event) => {
            this.handleSecurityAlert(event);
        });

        // Set up metrics collection
        this.enhancedLogger.on('metrics', (metrics) => {
            this.handleMetricsUpdate(metrics);
        });

        // Log system startup
        this.enhancedLogger.info('Black-G CLI system initializing', {
            version: '2.0.0',
            nodeVersion: process.version,
            platform: process.platform,
            pid: process.pid
        });
    }

    handleSystemAlert(alert) {
        console.log(`${colors.red}🚨 SYSTEM ALERT: ${alert.type}${colors.reset}`);
        console.log(`${colors.yellow}Details: ${JSON.stringify(alert.details)}${colors.reset}`);

        // In production, this could send notifications, emails, etc.
        if (alert.severity === 'CRITICAL') {
            console.log(`${colors.red}⚠️  Critical alert - immediate attention required!${colors.reset}`);
        }
    }

    handleSecurityAlert(event) {
        console.log(`${colors.red}🔒 SECURITY EVENT: ${event.eventType}${colors.reset}`);
        console.log(`${colors.yellow}Severity: ${event.severity}${colors.reset}`);
        console.log(`${colors.yellow}Details: ${JSON.stringify(event.details)}${colors.reset}`);

        // Log security events for audit trail
        this.enhancedLogger.error('Security event detected', {
            type: 'security_audit',
            event: event
        });
    }

    handleMetricsUpdate(metrics) {
        // Store metrics for dashboard or monitoring systems
        // In production, this could send to monitoring services like Prometheus, DataDog, etc.
        this.enhancedLogger.trace('Metrics updated', {
            type: 'metrics_update',
            metrics: {
                memoryUsage: Math.round(metrics.memory.heapUsed / 1024 / 1024), // MB
                uptime: Math.round(metrics.uptime),
                activeHandles: metrics.activeHandles
            }
        });
    }

    async initializeSystem() {
        this.initializeAI();
        await this.toolSelector.initialize();
        this.setupReadline();
        this.showBanner();
    }

    initializeAI() {
        const apiKey = process.env.GEMINI_API_KEY;
        if (!apiKey) {
            this.printError('GEMINI_API_KEY environment variable not set.');
            this.printInfo('Please set your Gemini API key in the .env file');
            process.exit(1);
        }
        
        this.genAI = new GoogleGenerativeAI(apiKey);
        this.model = this.genAI.getGenerativeModel({ model: 'gemini-2.5-flash' });
        
        // Initialize chat with Black-G system prompt
        this.initializeChat();
    }

    setupReadline() {
        this.rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout,
            prompt: `${colors.cyan}Black-G>${colors.reset} `
        });

        this.rl.on('line', (input) => this.handleUserInput(input.trim()));
        this.rl.on('close', () => this.shutdown());
    }

    showBanner() {
        console.clear();
        console.log(`${colors.cyan}
╔══════════════════════════════════════════════════════════════════════╗
║                                                                      ║
║    🤖 BLACK-G CLI - AI PENETRATION TESTING SYSTEM                   ║
║                                                                      ║
║    Specialized Attack Surface Management for Parrot OS              ║
║    Natural Language → Intelligent Tool Selection → Professional Reports ║
║                                                                      ║
║    🎯 ASM Categories: Domain Vulns • SSL/TLS • Config • Ports       ║
║                      Reputation • Cloud Security • Auth Discovery   ║
║                                                                      ║
╚══════════════════════════════════════════════════════════════════════╝${colors.reset}

${colors.yellow}🚀 Welcome to Black-G CLI!${colors.reset}
${colors.white}Type your security testing requests in natural language.${colors.reset}

${colors.green}Examples:${colors.reset}
  • "Perform passive reconnaissance on example.com"
  • "Run comprehensive ASM analysis on 192.168.1.0/24"  
  • "Check SSL vulnerabilities on my web servers"
  • "Scan for open ports and services on target.com"

${colors.blue}Commands:${colors.reset} help | status | history | clear | exit

`);
        this.rl.prompt();
    }

    async initializeChat() {
        const systemPrompt = this.getBlackGSystemPrompt();
        this.chatHistory = [
            {
                role: 'user',
                parts: [{ text: systemPrompt }]
            },
            {
                role: 'model', 
                parts: [{ text: 'Black-G AI Penetration Testing Assistant initialized. Ready to assist with Attack Surface Management and security testing. Please describe your security testing objective.' }]
            }
        ];
    }

    getBlackGSystemPrompt() {
        // Get current tool availability status
        const toolStatus = this.getToolAvailabilityStatus();

        return `You are Black-G, an advanced AI penetration testing assistant specialized in Attack Surface Management for Parrot OS Security Edition.

CORE IDENTITY:
- Expert penetration tester and security analyst with 15+ years experience
- Specialized in Attack Surface Management (ASM) and OWASP methodologies
- Integrated with Parrot OS security tools and custom automation
- Focused on professional, ethical security testing with business impact analysis

ENHANCED CAPABILITIES:
- Advanced natural language interpretation with context awareness
- Intelligent tool selection based on target characteristics and availability
- Real-time analysis with correlation across multiple data sources
- Professional report generation with executive summaries
- Comprehensive ASM analysis across 7 weighted categories
- Risk scoring with CVSS-aligned methodology
- Automated vulnerability correlation and false positive reduction

ASM ANALYSIS CATEGORIES (Weighted):
1. Domain/IP Vulnerabilities (25%) - Network and application vulnerability assessment
2. SSL/TLS Certificate Analysis (20%) - Certificate validation, cipher analysis, protocol security
3. Open Ports & Services (20%) - Port scanning, service enumeration, banner analysis
4. Configuration Issues (15%) - Misconfigurations, security headers, default credentials
5. Cloud Security Assessment (10%) - Cloud-specific vulnerabilities, IAM misconfigurations
6. IP/Domain Reputation (5%) - Threat intelligence, blacklist checks, historical data
7. Authentication Discovery (5%) - Login mechanisms, MFA detection, password policies

CURRENT TOOL STATUS:
${toolStatus}

OPERATIONAL MODES:
- PASSIVE: Subdomain enumeration, DNS analysis, OSINT (no direct target interaction)
- ACTIVE: Port scanning, service detection, vulnerability assessment (requires authorization)
- COMPREHENSIVE: Full ASM analysis across all 7 categories with correlation
- TARGETED: Specific vulnerability, service, or compliance analysis
- STEALTH: Low-profile scanning with evasion techniques

ENHANCED RESPONSE FORMAT:
Always structure your responses with these sections:

🎯 **OBJECTIVE:** Clear understanding of the user's request with scope definition
📊 **STRATEGY:** Selected approach, tool sequence, and methodology rationale
🔍 **EXECUTION_PLAN:** Detailed step-by-step execution plan with phases
⚠️ **RISK_ASSESSMENT:** Potential risks, detection likelihood, and mitigation strategies
📋 **EXPECTED_OUTCOMES:** Specific deliverables and success criteria

For tool execution, provide commands in this enhanced format:
COMMAND: tool_name arguments
DESCRIPTION: Detailed explanation of command purpose and expected output
CATEGORY: Which ASM category this addresses (with weight consideration)
RISK_LEVEL: LOW/MEDIUM/HIGH based on target interaction and detection risk
DEPENDENCIES: Any prerequisite commands or data requirements

ADVANCED ANALYSIS FEATURES:
- Correlation of findings across multiple tools and categories
- False positive identification and filtering
- Risk scoring with business impact assessment
- Compliance mapping (OWASP, NIST, ISO 27001)
- Automated remediation recommendations with priority ranking

ETHICAL GUIDELINES & LEGAL COMPLIANCE:
- MANDATORY: Explicit written authorization required for all active scanning
- Rate limiting: Respect target infrastructure and avoid DoS conditions
- Responsible disclosure: Follow coordinated vulnerability disclosure practices
- Legal compliance: Adhere to local laws, CFAA, GDPR, and industry regulations
- Stealth operations: Minimize detection and impact on production systems
- Documentation: Maintain detailed audit trails for all activities

CONTEXT AWARENESS:
- Remember previous scan results and build upon them
- Correlate findings across multiple sessions
- Adapt tool selection based on target responses and characteristics
- Provide progressive disclosure of information based on user expertise level

Remember: You are an expert AI assistant that provides strategic guidance for security testing. All commands require explicit user approval and proper authorization before execution.`;
    }

    getToolAvailabilityStatus() {
        if (!this.toolSelector.initialized) {
            return "Tool discovery not yet completed. Initializing...";
        }

        const available = [];
        const missing = [];

        for (const [tool, isAvailable] of this.toolSelector.toolAvailability) {
            if (isAvailable) {
                available.push(tool);
            } else {
                missing.push(tool);
            }
        }

        let status = `Available Tools: ${available.join(', ')}`;
        if (missing.length > 0) {
            status += `\nMissing Tools: ${missing.join(', ')} (commands will be adapted accordingly)`;
        }

        return status;
    }

    async handleUserInput(input) {
        if (!input) {
            this.rl.prompt();
            return;
        }

        // Handle built-in commands
        if (await this.handleBuiltinCommands(input)) {
            return;
        }

        // Process security testing request through AI
        await this.processSecurityRequest(input);
    }

    async handleBuiltinCommands(input) {
        const command = input.toLowerCase();
        
        switch (command) {
            case 'help':
                this.showHelp();
                return true;
                
            case 'status':
                this.showStatus();
                return true;
                
            case 'history':
                this.showHistory();
                return true;
                
            case 'clear':
                console.clear();
                this.showBanner();
                return true;
                
            case 'exit':
            case 'quit':
                this.shutdown();
                return true;
                
            default:
                return false;
        }
    }

    async processSecurityRequest(userInput) {
        try {
            this.printStatus('Processing your security testing request...');
            
            // Add user input to chat history
            this.chatHistory.push({
                role: 'user',
                parts: [{ text: userInput }]
            });

            // Get AI analysis and recommendations
            const chat = this.model.startChat({ history: this.chatHistory });
            const result = await chat.sendMessage(userInput);
            const response = await result.response;
            const aiResponse = response.text();

            // Add AI response to chat history
            this.chatHistory.push({
                role: 'model',
                parts: [{ text: aiResponse }]
            });

            // Parse and display AI response
            this.displayAIResponse(aiResponse);
            
            // Extract and handle any commands
            await this.handleAICommands(aiResponse, userInput);
            
        } catch (error) {
            this.printError(`AI processing failed: ${error.message}`);
            logger.error('Black-G AI processing error', { error: error.message, input: userInput });
        }
        
        this.rl.prompt();
    }

    displayAIResponse(response) {
        console.log(`\n${colors.magenta}🤖 Black-G Analysis:${colors.reset}\n`);
        
        // Parse structured response sections
        const sections = this.parseAIResponse(response);
        
        if (sections.objective) {
            console.log(`${colors.yellow}🎯 OBJECTIVE:${colors.reset} ${sections.objective}`);
        }
        
        if (sections.strategy) {
            console.log(`${colors.blue}📊 STRATEGY:${colors.reset} ${sections.strategy}`);
        }
        
        if (sections.execution_plan) {
            console.log(`${colors.green}🔍 EXECUTION PLAN:${colors.reset}\n${sections.execution_plan}`);
        }
        
        if (sections.risk_assessment) {
            console.log(`${colors.red}⚠️  RISK ASSESSMENT:${colors.reset} ${sections.risk_assessment}`);
        }
        
        if (sections.expected_outcomes) {
            console.log(`${colors.cyan}📋 EXPECTED OUTCOMES:${colors.reset} ${sections.expected_outcomes}`);
        }
        
        // Display any additional content
        if (sections.additional) {
            console.log(`\n${sections.additional}`);
        }
        
        console.log('');
    }

    parseAIResponse(response) {
        const sections = {};

        // Extract structured sections using regex patterns
        const patterns = {
            objective: /🎯\s*OBJECTIVE:\s*([^\n]*(?:\n(?!🎯|📊|🔍|⚠️|📋)[^\n]*)*)/i,
            strategy: /📊\s*STRATEGY:\s*([^\n]*(?:\n(?!🎯|📊|🔍|⚠️|📋)[^\n]*)*)/i,
            execution_plan: /🔍\s*EXECUTION[_\s]*PLAN:\s*([^\n]*(?:\n(?!🎯|📊|🔍|⚠️|📋)[^\n]*)*)/i,
            risk_assessment: /⚠️\s*RISK[_\s]*ASSESSMENT:\s*([^\n]*(?:\n(?!🎯|📊|🔍|⚠️|📋)[^\n]*)*)/i,
            expected_outcomes: /📋\s*EXPECTED[_\s]*OUTCOMES:\s*([^\n]*(?:\n(?!🎯|📊|🔍|⚠️|📋)[^\n]*)*)/i
        };

        for (const [key, pattern] of Object.entries(patterns)) {
            const match = response.match(pattern);
            if (match) {
                sections[key] = match[1].trim();
            }
        }

        // Extract any remaining content as additional
        let remainingContent = response;
        for (const section of Object.values(sections)) {
            remainingContent = remainingContent.replace(section, '');
        }

        // Clean up remaining content
        remainingContent = remainingContent
            .replace(/🎯\s*OBJECTIVE:\s*/gi, '')
            .replace(/📊\s*STRATEGY:\s*/gi, '')
            .replace(/🔍\s*EXECUTION[_\s]*PLAN:\s*/gi, '')
            .replace(/⚠️\s*RISK[_\s]*ASSESSMENT:\s*/gi, '')
            .replace(/📋\s*EXPECTED[_\s]*OUTCOMES:\s*/gi, '')
            .trim();

        if (remainingContent) {
            sections.additional = remainingContent;
        }

        return sections;
    }

    async prepareContextualInput(userInput) {
        let contextualInput = userInput;

        // Add session context if available
        if (this.currentSession) {
            contextualInput += `\n\nCONTEXT: Previous scan session targeting ${this.currentSession.target} is in progress.`;
        }

        // Add tool availability context
        const toolStatus = this.getToolAvailabilityStatus();
        contextualInput += `\n\nTOOL_STATUS: ${toolStatus}`;

        // Add recent findings context if available
        if (this.chatHistory.length > 2) {
            const recentFindings = this.extractRecentFindings();
            if (recentFindings) {
                contextualInput += `\n\nRECENT_FINDINGS: ${recentFindings}`;
            }
        }

        return contextualInput;
    }

    extractRecentFindings() {
        // Extract key findings from recent chat history
        const recentMessages = this.chatHistory.slice(-4); // Last 4 messages
        let findings = '';

        for (const message of recentMessages) {
            if (message.role === 'model' && message.parts[0].text.includes('findings')) {
                // Extract key findings (simplified)
                const text = message.parts[0].text;
                const findingsMatch = text.match(/findings?[:\s]([^.]+)/i);
                if (findingsMatch) {
                    findings += findingsMatch[1] + '; ';
                }
            }
        }

        return findings.trim();
    }

    extractAndValidateCommands(response) {
        const commands = this.extractCommands(response);

        // Enhanced validation for each command
        return commands.map(cmd => {
            const validation = this.validateSingleCommand(cmd);
            return {
                ...cmd,
                isValid: validation.isValid,
                validationError: validation.error,
                riskLevel: this.assessSingleCommandRisk(cmd.command)
            };
        });
    }

    validateSingleCommand(cmd) {
        const toolName = cmd.command.split(' ')[0];

        // Check tool availability
        if (!this.toolSelector.isToolAvailable(toolName)) {
            return {
                isValid: false,
                error: `Tool '${toolName}' is not available`
            };
        }

        // Check for security issues
        if (this.isCommandSyntaxInvalid(cmd.command)) {
            return {
                isValid: false,
                error: 'Command contains potentially unsafe syntax'
            };
        }

        return { isValid: true, error: null };
    }

    assessSingleCommandRisk(command) {
        const highRiskTools = ['masscan', 'nmap', 'nuclei'];
        const mediumRiskTools = ['gobuster', 'sslscan'];
        const lowRiskTools = ['subfinder', 'amass', 'whois', 'dig'];

        const toolName = command.split(' ')[0];

        if (highRiskTools.includes(toolName)) return 'HIGH';
        if (mediumRiskTools.includes(toolName)) return 'MEDIUM';
        if (lowRiskTools.includes(toolName)) return 'LOW';

        return 'UNKNOWN';
    }

    assessCommandRisk(commands) {
        const riskLevels = commands.map(cmd => cmd.riskLevel || 'UNKNOWN');
        const hasHighRisk = riskLevels.includes('HIGH');
        const hasMediumRisk = riskLevels.includes('MEDIUM');

        return {
            hasHighRisk,
            hasMediumRisk,
            overallRisk: hasHighRisk ? 'HIGH' : hasMediumRisk ? 'MEDIUM' : 'LOW'
        };
    }

    async handleAICommands(aiResponse, originalInput) {
        // Extract commands from AI response
        const commands = this.extractCommands(aiResponse);

        if (commands.length === 0) {
            return;
        }

        console.log(`${colors.yellow}🔧 Detected ${commands.length} command(s) for execution:${colors.reset}\n`);

        for (let i = 0; i < commands.length; i++) {
            const cmd = commands[i];
            console.log(`${colors.white}${i + 1}. ${cmd.command}${colors.reset}`);
            if (cmd.description) {
                console.log(`   ${colors.blue}Description:${colors.reset} ${cmd.description}`);
            }
            if (cmd.category) {
                console.log(`   ${colors.green}ASM Category:${colors.reset} ${cmd.category}`);
            }
            console.log('');
        }

        // Ask for user confirmation
        const shouldExecute = await this.askConfirmation('Execute these commands?');

        if (shouldExecute) {
            await this.executeCommands(commands, originalInput);
        } else {
            this.printInfo('Command execution cancelled by user.');
        }
    }

    extractCommands(response) {
        const commands = [];
        const lines = response.split('\n');

        let currentCommand = null;

        for (const line of lines) {
            const trimmedLine = line.trim();

            // Look for COMMAND: pattern
            if (trimmedLine.startsWith('COMMAND:')) {
                if (currentCommand) {
                    commands.push(currentCommand);
                }
                currentCommand = {
                    command: trimmedLine.substring(8).trim(),
                    description: '',
                    category: ''
                };
            }
            // Look for DESCRIPTION: pattern
            else if (trimmedLine.startsWith('DESCRIPTION:') && currentCommand) {
                currentCommand.description = trimmedLine.substring(12).trim();
            }
            // Look for CATEGORY: pattern
            else if (trimmedLine.startsWith('CATEGORY:') && currentCommand) {
                currentCommand.category = trimmedLine.substring(9).trim();
            }
        }

        // Add the last command if exists
        if (currentCommand) {
            commands.push(currentCommand);
        }

        return commands;
    }

    async askConfirmation(question) {
        return new Promise((resolve) => {
            this.rl.question(`${colors.yellow}❓ ${question} (y/n): ${colors.reset}`, (answer) => {
                resolve(answer.toLowerCase().startsWith('y'));
            });
        });
    }

    async executeCommands(commands, originalInput) {
        this.activeScan = true;
        this.currentSession = {
            id: this.generateSessionId(),
            target: this.extractTarget(originalInput),
            startTime: new Date().toISOString(),
            commands: commands,
            results: []
        };

        // Log scan start
        this.enhancedLogger.logScanStart(this.currentSession.target, this.currentSession.id);
        this.printStatus(`Starting scan session: ${this.currentSession.id}`);

        for (let i = 0; i < commands.length; i++) {
            const cmd = commands[i];
            console.log(`\n${colors.cyan}[${i + 1}/${commands.length}] Executing: ${cmd.command}${colors.reset}`);

            try {
                const result = await this.executeCommand(cmd.command);
                this.currentSession.results.push({
                    command: cmd.command,
                    description: cmd.description,
                    category: cmd.category,
                    result: result,
                    timestamp: new Date().toISOString()
                });

                this.printSuccess(`Command completed successfully`);

                // Brief pause between commands
                await this.sleep(1000);

            } catch (error) {
                this.printError(`Command failed: ${error.message}`);
                this.currentSession.results.push({
                    command: cmd.command,
                    description: cmd.description,
                    category: cmd.category,
                    error: error.message,
                    timestamp: new Date().toISOString()
                });
            }
        }

        this.activeScan = false;

        // Log scan completion
        const scanDuration = Date.now() - new Date(this.currentSession.startTime).getTime();
        const successfulCommands = this.currentSession.results.filter(r => r.status === 'SUCCESS').length;
        const scanSuccess = successfulCommands > 0;

        this.enhancedLogger.logScanComplete(
            this.currentSession.target,
            this.currentSession.id,
            scanSuccess,
            scanDuration,
            this.currentSession.results
        );

        await this.finalizeScanSession();
    }

    async validateCommands(commands) {
        const errors = [];
        const invalidCommands = [];

        for (let i = 0; i < commands.length; i++) {
            const cmd = commands[i];
            const parts = cmd.command.split(' ');
            const toolName = parts[0];

            // Check tool availability
            if (!this.toolSelector.isToolAvailable(toolName)) {
                errors.push(`Tool '${toolName}' is not available`);
                invalidCommands.push(i);
                continue;
            }

            // Basic command syntax validation
            if (this.isCommandSyntaxInvalid(cmd.command)) {
                errors.push(`Invalid syntax in command: ${cmd.command}`);
                invalidCommands.push(i);
            }
        }

        return {
            hasErrors: errors.length > 0,
            errors: errors,
            invalidCommands: invalidCommands
        };
    }

    isCommandSyntaxInvalid(command) {
        // Basic syntax checks for security
        if (command.includes('&&') || command.includes('||') || command.includes(';')) {
            return true; // Prevent command chaining
        }

        if (command.includes('$(') || command.includes('`')) {
            return true; // Prevent command substitution
        }

        return false;
    }

    async executeCommandWithRetry(command, maxRetries = 2) {
        let lastError;

        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                if (attempt > 1) {
                    console.log(`${colors.yellow}Retry attempt ${attempt}/${maxRetries}${colors.reset}`);
                    await this.sleep(1000 * attempt); // Progressive delay
                }

                return await this.executeCommand(command);
            } catch (error) {
                lastError = error;

                if (attempt < maxRetries) {
                    console.log(`${colors.yellow}Command failed, retrying...${colors.reset}`);
                }
            }
        }

        throw lastError;
    }

    getEnvironmentInfo() {
        return {
            platform: process.platform,
            nodeVersion: process.version,
            workingDirectory: process.cwd(),
            pathEnv: process.env.PATH,
            user: process.env.USER || process.env.USERNAME,
            shell: process.env.SHELL
        };
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    async executeCommand(command) {
        const startTime = Date.now();
        const toolName = command.split(' ')[0];

        return new Promise((resolve, reject) => {
            const parts = command.split(' ');
            const args = parts.slice(1);

            // Use discovered tool path if available
            const toolPath = this.toolSelector.getToolPath(toolName);

            // Check if tool is available before execution
            if (!this.toolSelector.isToolAvailable(toolName)) {
                const error = new Error(`Tool '${toolName}' is not available. Please install it first.`);
                this.enhancedLogger.logToolExecution(toolName, command, false, Date.now() - startTime, error);
                reject(error);
                return;
            }

            let output = '';
            let errorOutput = '';

            const child = spawn(toolPath, args, {
                stdio: ['pipe', 'pipe', 'pipe'],
                env: process.env, // Inherit full environment
                shell: false // Use direct execution for better control
            });

            child.stdout.on('data', (data) => {
                const chunk = data.toString();
                process.stdout.write(chunk);
                output += chunk;
            });

            child.stderr.on('data', (data) => {
                const chunk = data.toString();
                process.stderr.write(chunk);
                errorOutput += chunk;
            });

            child.on('close', (code) => {
                const duration = Date.now() - startTime;

                if (code === 0) {
                    this.enhancedLogger.logToolExecution(toolName, command, true, duration);
                    resolve({
                        exitCode: code,
                        output: output,
                        errorOutput: errorOutput
                    });
                } else {
                    const error = new Error(`Command exited with code ${code}: ${errorOutput}`);
                    this.enhancedLogger.logToolExecution(toolName, command, false, duration, error);
                    reject(error);
                }
            });

            child.on('error', (err) => {
                const duration = Date.now() - startTime;
                const error = new Error(`Failed to execute command: ${err.message}`);
                this.enhancedLogger.logToolExecution(toolName, command, false, duration, error);
                reject(error);
            });
        });
    }

    async finalizeScanSession() {
        this.printStatus('Scan session completed. Analyzing results...');

        // Save session results
        await this.saveSessionResults();

        // Generate AI analysis of results
        await this.generateSessionAnalysis();

        // Offer to generate report
        const shouldGenerateReport = await this.askConfirmation('Generate professional report?');
        if (shouldGenerateReport) {
            await this.generateReport();
        }

        this.printSuccess(`Scan session ${this.currentSession.id} completed successfully`);
        this.currentSession = null;
    }

    async saveSessionResults() {
        try {
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const filename = `${timestamp}_${this.currentSession.id}_black-g-session.json`;
            const filepath = path.join('./scan_reports', filename);

            const sessionData = {
                ...this.currentSession,
                endTime: new Date().toISOString(),
                totalCommands: this.currentSession.commands.length,
                successfulCommands: this.currentSession.results.filter(r => !r.error).length,
                failedCommands: this.currentSession.results.filter(r => r.error).length
            };

            fs.writeFileSync(filepath, JSON.stringify(sessionData, null, 2));
            this.printInfo(`Session results saved to: ${filepath}`);

        } catch (error) {
            this.printError(`Failed to save session results: ${error.message}`);
        }
    }

    async generateSessionAnalysis() {
        try {
            const resultsText = this.formatResultsForAI();
            const analysisPrompt = `Analyze the following scan results and provide insights:

${resultsText}

Please provide:
1. Summary of findings
2. Risk assessment
3. Key vulnerabilities or issues discovered
4. Recommendations for next steps
5. Overall security posture assessment`;

            const chat = this.model.startChat({ history: this.chatHistory });
            const result = await chat.sendMessage(analysisPrompt);
            const response = await result.response;
            const analysis = response.text();

            console.log(`\n${colors.magenta}🧠 AI Analysis of Scan Results:${colors.reset}\n`);
            console.log(analysis);

            // Save analysis to session
            this.currentSession.aiAnalysis = analysis;

        } catch (error) {
            this.printError(`Failed to generate AI analysis: ${error.message}`);
        }
    }

    formatResultsForAI() {
        let resultsText = `Scan Session: ${this.currentSession.id}\n`;
        resultsText += `Target: ${this.currentSession.target}\n`;
        resultsText += `Commands Executed: ${this.currentSession.results.length}\n\n`;

        for (const result of this.currentSession.results) {
            resultsText += `Command: ${result.command}\n`;
            resultsText += `Category: ${result.category}\n`;
            if (result.error) {
                resultsText += `Status: FAILED - ${result.error}\n`;
            } else {
                resultsText += `Status: SUCCESS\n`;
                resultsText += `Output: ${result.result.output.substring(0, 1000)}...\n`;
            }
            resultsText += `---\n`;
        }

        return resultsText;
    }

    async generateReport() {
        try {
            this.printStatus('Generating professional report...');

            // Use existing report generation system
            const reportData = this.prepareReportData();
            await this.reporter.generateReport(reportData);

            this.printSuccess('Report generated successfully');

        } catch (error) {
            this.printError(`Report generation failed: ${error.message}`);
        }
    }

    prepareReportData() {
        return {
            sessionId: this.currentSession.id,
            target: this.currentSession.target,
            startTime: this.currentSession.startTime,
            endTime: new Date().toISOString(),
            commands: this.currentSession.commands,
            results: this.currentSession.results,
            aiAnalysis: this.currentSession.aiAnalysis || 'No AI analysis available'
        };
    }

    // Utility methods
    generateSessionId() {
        return 'bg-' + Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    extractTarget(input) {
        // Simple target extraction - can be enhanced
        const domainRegex = /(?:https?:\/\/)?(?:www\.)?([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}/g;
        const ipRegex = /\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}(?:\/[0-9]{1,2})?\b/g;

        const domainMatch = input.match(domainRegex);
        const ipMatch = input.match(ipRegex);

        return domainMatch?.[0] || ipMatch?.[0] || 'unknown';
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Display methods
    printStatus(message) {
        console.log(`${colors.green}[+]${colors.reset} ${message}`);
    }

    printError(message) {
        console.log(`${colors.red}[-]${colors.reset} ${message}`);
    }

    printWarning(message) {
        console.log(`${colors.yellow}[!]${colors.reset} ${message}`);
    }

    printInfo(message) {
        console.log(`${colors.blue}[i]${colors.reset} ${message}`);
    }

    printSuccess(message) {
        console.log(`${colors.green}[✓]${colors.reset} ${message}`);
    }

    showHelp() {
        console.log(`\n${colors.cyan}Black-G CLI Help${colors.reset}\n`);
        console.log(`${colors.yellow}Natural Language Commands:${colors.reset}`);
        console.log('  • "Perform passive reconnaissance on example.com"');
        console.log('  • "Run comprehensive ASM analysis on target.com"');
        console.log('  • "Check SSL vulnerabilities on my web servers"');
        console.log('  • "Scan for open ports on 192.168.1.0/24"');
        console.log('  • "Find subdomains for company.com"');
        console.log('  • "Analyze attack surface of my application"');

        console.log(`\n${colors.yellow}Built-in Commands:${colors.reset}`);
        console.log('  help     - Show this help message');
        console.log('  status   - Show current system status');
        console.log('  history  - Show command history');
        console.log('  clear    - Clear screen and show banner');
        console.log('  exit     - Exit Black-G CLI');

        console.log(`\n${colors.yellow}ASM Categories:${colors.reset}`);
        console.log('  1. Domain/IP Vulnerabilities');
        console.log('  2. SSL/TLS Certificate Analysis');
        console.log('  3. Configuration Issues');
        console.log('  4. Open Ports & Services');
        console.log('  5. IP/Domain Reputation');
        console.log('  6. Cloud Security Assessment');
        console.log('  7. Authentication Discovery');

        console.log(`\n${colors.blue}Tips:${colors.reset}`);
        console.log('  • Be specific about your target and objectives');
        console.log('  • Black-G will ask for confirmation before executing commands');
        console.log('  • All scan results are saved and can be used for reporting');
        console.log('  • Use natural language - Black-G understands context');
        console.log('');
    }

    showStatus() {
        console.log(`\n${colors.cyan}📊 Black-G CLI Enhanced Status${colors.reset}`);

        // Basic status
        console.log(`\n${colors.white}System Status:${colors.reset}`);
        console.log(`  AI Engine: ${this.model ? colors.green + 'Connected' + colors.reset : colors.red + 'Disconnected' + colors.reset}`);
        console.log(`  Active Scan: ${this.activeScan ? colors.green + 'Yes' + colors.reset : 'No'}`);
        console.log(`  Chat History: ${this.chatHistory.length} messages`);

        if (this.currentSession) {
            console.log(`\n${colors.white}Current Session:${colors.reset}`);
            console.log(`  Session ID: ${this.currentSession.id}`);
            console.log(`  Target: ${this.currentSession.target}`);
            console.log(`  Started: ${new Date(this.currentSession.startTime).toLocaleString()}`);
            console.log(`  Commands: ${this.currentSession.commands?.length || 0}`);
            console.log(`  Results: ${this.currentSession.results?.length || 0}`);
        }

        // Enhanced metrics
        const metrics = this.enhancedLogger.getMetrics();
        console.log(`\n${colors.white}Performance Metrics:${colors.reset}`);
        console.log(`  Uptime: ${this.formatUptime(metrics.uptime)}`);
        console.log(`  Total Scans: ${metrics.totalRequests}`);
        console.log(`  Success Rate: ${metrics.successRate}%`);
        console.log(`  Average Response Time: ${metrics.averageResponseTime}ms`);

        // Tool performance
        if (Object.keys(metrics.toolPerformance).length > 0) {
            console.log(`\n${colors.white}Tool Performance:${colors.reset}`);
            for (const [tool, stats] of Object.entries(metrics.toolPerformance)) {
                const statusColor = stats.successRate >= 80 ? colors.green : stats.successRate >= 60 ? colors.yellow : colors.red;
                console.log(`  ${tool}: ${statusColor}${stats.successRate}%${colors.reset} (${stats.total} executions, avg: ${stats.averageDuration}ms)`);
            }
        } else {
            console.log(`\n${colors.white}Tool Availability:${colors.reset}`);
            if (this.toolSelector.initialized) {
                for (const [tool, available] of this.toolSelector.toolAvailability) {
                    const statusColor = available ? colors.green : colors.red;
                    const statusText = available ? 'Available' : 'Missing';
                    console.log(`  ${tool}: ${statusColor}${statusText}${colors.reset}`);
                }
            } else {
                console.log(`  ${colors.yellow}Tool discovery in progress...${colors.reset}`);
            }
        }

        // System resources
        const memUsage = process.memoryUsage();
        console.log(`\n${colors.white}System Resources:${colors.reset}`);
        console.log(`  Memory Usage: ${Math.round(memUsage.heapUsed / 1024 / 1024)}MB / ${Math.round(memUsage.heapTotal / 1024 / 1024)}MB`);
        console.log(`  CPU Time: ${Math.round(process.cpuUsage().user / 1000)}ms user, ${Math.round(process.cpuUsage().system / 1000)}ms system`);

        // Error summary
        if (Object.keys(metrics.errorCounts).length > 0) {
            console.log(`\n${colors.white}Error Summary:${colors.reset}`);
            for (const [errorType, count] of Object.entries(metrics.errorCounts)) {
                console.log(`  ${errorType}: ${colors.red}${count}${colors.reset} occurrences`);
            }
        }

        console.log('');
        this.rl.prompt();
    }

    formatUptime(milliseconds) {
        const seconds = Math.floor(milliseconds / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        const days = Math.floor(hours / 24);

        if (days > 0) return `${days}d ${hours % 24}h ${minutes % 60}m`;
        if (hours > 0) return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
        if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
        return `${seconds}s`;
    }

    showHistory() {
        console.log(`\n${colors.cyan}Command History${colors.reset}\n`);

        const userMessages = this.chatHistory.filter(msg => msg.role === 'user');

        if (userMessages.length <= 1) { // First message is system prompt
            console.log('No command history available.');
        } else {
            userMessages.slice(1).forEach((msg, index) => {
                console.log(`${colors.yellow}${index + 1}.${colors.reset} ${msg.parts[0].text}`);
            });
        }

        console.log('');
    }

    shutdown() {
        console.log(`\n${colors.yellow}🛑 Shutting down Black-G CLI...${colors.reset}`);

        if (this.activeScan) {
            console.log(`${colors.red}⚠️  Warning: Active scan in progress will be terminated.${colors.reset}`);
        }

        console.log(`${colors.green}Thank you for using Black-G CLI!${colors.reset}`);
        console.log(`${colors.blue}Stay secure! 🛡️${colors.reset}\n`);

        process.exit(0);
    }
}

// Supporting Classes

class ToolSelector {
    constructor() {
        this.toolMap = {
            'passive': ['subfinder', 'amass', 'whois', 'dig'],
            'asm_full': ['subfinder', 'nmap', 'nuclei', 'sslscan'],
            'vulnerability': ['nmap', 'nuclei'],
            'ssl_analysis': ['sslscan', 'testssl'],
            'port_scan': ['nmap', 'masscan'],
            'subdomain': ['subfinder', 'amass'],
            'web_scan': ['gobuster', 'nuclei']
        };

        // Enhanced tool discovery with multiple path checking
        this.toolPaths = new Map();
        this.toolAvailability = new Map();
        this.initialized = false;
    }

    async initialize() {
        if (this.initialized) return;

        console.log(`${colors.yellow}[+] Initializing tool discovery...${colors.reset}`);

        // Common tool installation paths for Parrot OS
        const searchPaths = [
            '/usr/bin',
            '/usr/local/bin',
            '/home/<USER>/go/bin',
            '/snap/bin',
            '/opt',
            process.env.HOME + '/go/bin',
            process.env.HOME + '/.cargo/bin'
        ];

        const allTools = [...new Set(Object.values(this.toolMap).flat())];

        for (const tool of allTools) {
            await this.discoverTool(tool, searchPaths);
        }

        this.printToolStatus();
        this.initialized = true;
    }

    async discoverTool(toolName, searchPaths) {
        // First try 'which' command to find tool
        try {
            const result = await this.executeWhichCommand(toolName);
            if (result && result.trim()) {
                this.toolPaths.set(toolName, result.trim());
                this.toolAvailability.set(toolName, true);
                return;
            }
        } catch (error) {
            // Continue to manual path search
        }

        // Manual path search
        for (const searchPath of searchPaths) {
            const fullPath = path.join(searchPath, toolName);
            if (fs.existsSync(fullPath)) {
                try {
                    fs.accessSync(fullPath, fs.constants.X_OK);
                    this.toolPaths.set(toolName, fullPath);
                    this.toolAvailability.set(toolName, true);
                    return;
                } catch (error) {
                    // Tool exists but not executable
                }
            }
        }

        // Tool not found
        this.toolAvailability.set(toolName, false);
    }

    async executeWhichCommand(toolName) {
        return new Promise((resolve) => {
            const child = spawn('which', [toolName], {
                stdio: ['pipe', 'pipe', 'pipe'],
                env: process.env
            });

            let output = '';
            child.stdout.on('data', (data) => {
                output += data.toString();
            });

            child.on('close', (code) => {
                resolve(code === 0 ? output : null);
            });

            child.on('error', () => {
                resolve(null);
            });
        });
    }

    printToolStatus() {
        console.log(`${colors.cyan}\n🔧 Tool Discovery Results:${colors.reset}`);
        const availableTools = [];
        const missingTools = [];

        for (const [tool, available] of this.toolAvailability) {
            if (available) {
                const toolPath = this.toolPaths.get(tool);
                console.log(`${colors.green}[✓] ${tool}: ${toolPath}${colors.reset}`);
                availableTools.push(tool);
            } else {
                console.log(`${colors.red}[✗] ${tool}: Not found${colors.reset}`);
                missingTools.push(tool);
            }
        }

        if (missingTools.length > 0) {
            console.log(`${colors.yellow}\n⚠️  Missing tools detected. Install with:${colors.reset}`);
            console.log(`${colors.white}sudo apt update && sudo apt install ${missingTools.filter(t => !['subfinder', 'amass', 'nuclei'].includes(t)).join(' ')}${colors.reset}`);
            if (missingTools.includes('subfinder')) {
                console.log(`${colors.white}go install -v github.com/projectdiscovery/subfinder/v2/cmd/subfinder@latest${colors.reset}`);
            }
            if (missingTools.includes('amass')) {
                console.log(`${colors.white}go install -v github.com/owasp-amass/amass/v4/...@master${colors.reset}`);
            }
            if (missingTools.includes('nuclei')) {
                console.log(`${colors.white}go install -v github.com/projectdiscovery/nuclei/v3/cmd/nuclei@latest${colors.reset}`);
            }
        }

        console.log(`${colors.cyan}\nTools Available: ${availableTools.length}/${this.toolAvailability.size}${colors.reset}\n`);
    }

    getToolPath(toolName) {
        return this.toolPaths.get(toolName) || toolName;
    }

    isToolAvailable(toolName) {
        return this.toolAvailability.get(toolName) === true;
    }

    analyzeRequest(userInput) {
        const intent = this.parseIntent(userInput);
        const target = this.extractTarget(userInput);
        const scanType = this.determineScanType(intent);

        return {
            intent,
            target,
            scanType,
            tools: this.selectTools(scanType, target),
            strategy: this.buildStrategy(scanType, target)
        };
    }

    parseIntent(input) {
        const lowerInput = input.toLowerCase();

        if (lowerInput.includes('passive') || lowerInput.includes('reconnaissance')) {
            return 'passive_recon';
        } else if (lowerInput.includes('asm') || lowerInput.includes('attack surface')) {
            return 'asm_analysis';
        } else if (lowerInput.includes('ssl') || lowerInput.includes('certificate')) {
            return 'ssl_analysis';
        } else if (lowerInput.includes('port') || lowerInput.includes('scan')) {
            return 'port_scan';
        } else if (lowerInput.includes('subdomain')) {
            return 'subdomain_enum';
        } else if (lowerInput.includes('vulnerability') || lowerInput.includes('vuln')) {
            return 'vulnerability_scan';
        }

        return 'general_scan';
    }

    determineScanType(intent) {
        const scanTypeMap = {
            'passive_recon': 'passive',
            'asm_analysis': 'asm_full',
            'ssl_analysis': 'ssl_analysis',
            'port_scan': 'port_scan',
            'subdomain_enum': 'subdomain',
            'vulnerability_scan': 'vulnerability',
            'general_scan': 'asm_full'
        };

        return scanTypeMap[intent] || 'asm_full';
    }

    selectTools(scanType, target) {
        const requestedTools = this.toolMap[scanType] || this.toolMap['asm_full'];

        // Filter only available tools
        const availableTools = requestedTools.filter(tool =>
            this.toolAvailability.get(tool) === true
        );

        if (availableTools.length === 0) {
            console.log(`${colors.red}⚠️  No tools available for ${scanType} operation!${colors.reset}`);
            // Try to suggest alternatives
            const allAvailableTools = Array.from(this.toolAvailability.entries())
                .filter(([tool, available]) => available)
                .map(([tool]) => tool);

            if (allAvailableTools.length > 0) {
                console.log(`${colors.yellow}Available alternatives: ${allAvailableTools.join(', ')}${colors.reset}`);
                return allAvailableTools.slice(0, 3); // Return first 3 available tools
            }
            return [];
        }

        return availableTools;
    }

    buildStrategy(scanType, target) {
        const strategies = {
            'passive': 'Passive reconnaissance without direct target interaction',
            'asm_full': 'Comprehensive attack surface analysis across all categories',
            'ssl_analysis': 'SSL/TLS certificate and configuration analysis',
            'port_scan': 'Port scanning and service enumeration',
            'subdomain': 'Subdomain discovery and enumeration',
            'vulnerability': 'Vulnerability assessment and scanning'
        };

        return strategies[scanType] || 'General security assessment';
    }

    extractTarget(input) {
        const domainRegex = /(?:https?:\/\/)?(?:www\.)?([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}/g;
        const ipRegex = /\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}(?:\/[0-9]{1,2})?\b/g;

        const domainMatch = input.match(domainRegex);
        const ipMatch = input.match(ipRegex);

        return domainMatch?.[0] || ipMatch?.[0] || 'unknown';
    }
}

class ASMAnalyzer {
    constructor() {
        this.categories = {
            'Domain/IP Vulnerabilities': {
                weight: 0.25,
                indicators: ['subdomain', 'dns', 'domain', 'ip', 'network'],
                riskFactors: ['exposed_service', 'vulnerable_version', 'misconfiguration']
            },
            'SSL/TLS Certificate Analysis': {
                weight: 0.20,
                indicators: ['ssl', 'tls', 'certificate', 'cipher'],
                riskFactors: ['expired_cert', 'weak_cipher', 'self_signed', 'protocol_downgrade']
            },
            'Configuration Issues': {
                weight: 0.15,
                indicators: ['config', 'header', 'security', 'default'],
                riskFactors: ['default_credentials', 'missing_headers', 'debug_enabled']
            },
            'Open Ports & Services': {
                weight: 0.20,
                indicators: ['port', 'service', 'banner', 'version'],
                riskFactors: ['unnecessary_service', 'outdated_version', 'admin_interface']
            },
            'IP/Domain Reputation': {
                weight: 0.05,
                indicators: ['reputation', 'blacklist', 'threat'],
                riskFactors: ['blacklisted', 'malware_history', 'spam_source']
            },
            'Cloud Security Assessment': {
                weight: 0.10,
                indicators: ['cloud', 'aws', 'azure', 'gcp', 's3'],
                riskFactors: ['public_bucket', 'misconfigured_iam', 'exposed_metadata']
            },
            'Authentication Discovery': {
                weight: 0.05,
                indicators: ['login', 'auth', 'password', 'credential'],
                riskFactors: ['weak_auth', 'default_login', 'no_mfa']
            }
        };

        this.severityLevels = {
            'CRITICAL': { score: 10, color: 'red' },
            'HIGH': { score: 7, color: 'red' },
            'MEDIUM': { score: 5, color: 'yellow' },
            'LOW': { score: 3, color: 'green' },
            'INFO': { score: 1, color: 'cyan' }
        };
    }

    analyzeResults(results, target) {
        const analysis = {
            target: target,
            timestamp: new Date().toISOString(),
            categories: {},
            overallRisk: 'UNKNOWN',
            riskScore: 0,
            findings: [],
            recommendations: [],
            executionSummary: this.generateExecutionSummary(results),
            detailedFindings: this.extractDetailedFindings(results)
        };

        // Categorize results
        for (const result of results) {
            const category = this.categorizeResult(result);
            if (!analysis.categories[category]) {
                analysis.categories[category] = {
                    results: [],
                    riskScore: 0,
                    findings: [],
                    status: 'UNKNOWN'
                };
            }
            analysis.categories[category].results.push(result);
        }

        // Analyze each category
        for (const [categoryName, categoryData] of Object.entries(analysis.categories)) {
            const categoryAnalysis = this.analyzeCategoryResults(categoryName, categoryData.results);
            analysis.categories[categoryName] = { ...categoryData, ...categoryAnalysis };
        }

        // Calculate overall risk
        analysis.overallRisk = this.calculateOverallRisk(analysis.categories);
        analysis.riskScore = this.calculateRiskScore(analysis.categories);

        // Generate recommendations
        analysis.recommendations = this.generateRecommendations(analysis.categories, results);

        return analysis;
    }

    categorizeResult(result) {
        const command = result.command.toLowerCase();

        for (const [categoryName, categoryInfo] of Object.entries(this.categories)) {
            for (const indicator of categoryInfo.indicators) {
                if (command.includes(indicator)) {
                    return categoryName;
                }
            }
        }

        return 'General Assessment';
    }

    generateExecutionSummary(results) {
        const total = results.length;
        const successful = results.filter(r => r.status === 'SUCCESS' || (!r.error && r.result)).length;
        const failed = results.filter(r => r.status === 'FAILED' || r.error).length;
        const skipped = results.filter(r => r.status === 'SKIPPED').length;

        return {
            totalCommands: total,
            successfulCommands: successful,
            failedCommands: failed,
            skippedCommands: skipped,
            successRate: total > 0 ? Math.round((successful / total) * 100) : 0
        };
    }

    extractDetailedFindings(results) {
        const findings = [];

        for (const result of results) {
            if (result.result && result.result.output) {
                // Extract key information from command outputs
                const output = result.result.output;

                if (result.command.includes('nmap')) {
                    findings.push(...this.parseNmapOutput(output));
                } else if (result.command.includes('nuclei')) {
                    findings.push(...this.parseNucleiOutput(output));
                } else if (result.command.includes('sslscan')) {
                    findings.push(...this.parseSSLScanOutput(output));
                }
            }
        }

        return findings;
    }

    parseNmapOutput(output) {
        const findings = [];
        const lines = output.split('\n');

        for (const line of lines) {
            if (line.includes('open') && line.includes('/tcp')) {
                const portMatch = line.match(/(\d+)\/tcp\s+open\s+(\w+)/);
                if (portMatch) {
                    findings.push({
                        type: 'open_port',
                        port: portMatch[1],
                        service: portMatch[2],
                        severity: this.assessPortRisk(portMatch[1], portMatch[2])
                    });
                }
            }
        }

        return findings;
    }

    parseNucleiOutput(output) {
        const findings = [];
        const lines = output.split('\n');

        for (const line of lines) {
            if (line.includes('[') && (line.includes('critical') || line.includes('high') || line.includes('medium'))) {
                findings.push({
                    type: 'vulnerability',
                    description: line.trim(),
                    severity: this.extractSeverityFromNuclei(line)
                });
            }
        }

        return findings;
    }

    parseSSLScanOutput(output) {
        const findings = [];

        if (output.includes('SSLv2') || output.includes('SSLv3')) {
            findings.push({
                type: 'ssl_vulnerability',
                description: 'Deprecated SSL protocol detected',
                severity: 'HIGH'
            });
        }

        if (output.includes('RC4')) {
            findings.push({
                type: 'ssl_vulnerability',
                description: 'Weak RC4 cipher detected',
                severity: 'MEDIUM'
            });
        }

        return findings;
    }

    assessPortRisk(port, service) {
        const highRiskPorts = ['21', '23', '135', '139', '445', '1433', '3389'];
        const mediumRiskPorts = ['22', '25', '53', '80', '110', '143', '993', '995'];

        if (highRiskPorts.includes(port)) return 'HIGH';
        if (mediumRiskPorts.includes(port)) return 'MEDIUM';
        return 'LOW';
    }

    extractSeverityFromNuclei(line) {
        if (line.includes('critical')) return 'CRITICAL';
        if (line.includes('high')) return 'HIGH';
        if (line.includes('medium')) return 'MEDIUM';
        if (line.includes('low')) return 'LOW';
        return 'INFO';
    }

    analyzeCategoryResults(categoryName, results) {
        const successfulResults = results.filter(r => r.status === 'SUCCESS' || (!r.error && r.result));
        const failedResults = results.filter(r => r.status === 'FAILED' || r.error);

        let categoryRisk = 'UNKNOWN';
        let findings = [];
        let riskScore = 0;

        if (successfulResults.length === 0 && failedResults.length > 0) {
            categoryRisk = 'UNKNOWN';
            findings.push(`All ${categoryName} scans failed - unable to assess risk`);
        } else if (successfulResults.length > 0) {
            // Analyze successful results for security findings
            const securityFindings = this.extractSecurityFindings(categoryName, successfulResults);
            findings = securityFindings.findings;
            riskScore = securityFindings.riskScore;
            categoryRisk = this.scoreToRiskLevel(riskScore);
        }

        return {
            status: successfulResults.length > 0 ? 'COMPLETED' : 'FAILED',
            riskLevel: categoryRisk,
            riskScore: riskScore,
            findings: findings,
            successfulScans: successfulResults.length,
            failedScans: failedResults.length,
            totalScans: results.length
        };
    }

    extractSecurityFindings(categoryName, results) {
        const findings = [];
        let riskScore = 0;

        for (const result of results) {
            if (result.result && result.result.output) {
                const output = result.result.output.toLowerCase();

                // Category-specific analysis
                switch (categoryName) {
                    case 'Open Ports & Services':
                        const portFindings = this.analyzePortScanResults(output);
                        findings.push(...portFindings.findings);
                        riskScore += portFindings.score;
                        break;

                    case 'SSL/TLS Certificate Analysis':
                        const sslFindings = this.analyzeSSLResults(output);
                        findings.push(...sslFindings.findings);
                        riskScore += sslFindings.score;
                        break;

                    case 'Domain/IP Vulnerabilities':
                        const vulnFindings = this.analyzeVulnerabilityResults(output);
                        findings.push(...vulnFindings.findings);
                        riskScore += vulnFindings.score;
                        break;

                    default:
                        // Generic analysis for other categories
                        const genericFindings = this.analyzeGenericResults(output);
                        findings.push(...genericFindings.findings);
                        riskScore += genericFindings.score;
                }
            }
        }

        return { findings, riskScore };
    }

    analyzePortScanResults(output) {
        const findings = [];
        let score = 0;

        if (output.includes('open')) {
            findings.push('Open ports detected');
            score += 3;
        }

        if (output.includes('filtered')) {
            findings.push('Filtered ports detected (potential firewall)');
            score += 1;
        }

        return { findings, score };
    }

    analyzeSSLResults(output) {
        const findings = [];
        let score = 0;

        if (output.includes('sslv2') || output.includes('sslv3')) {
            findings.push('Deprecated SSL protocols detected');
            score += 7;
        }

        if (output.includes('rc4')) {
            findings.push('Weak RC4 cipher detected');
            score += 5;
        }

        return { findings, score };
    }

    analyzeVulnerabilityResults(output) {
        const findings = [];
        let score = 0;

        if (output.includes('critical')) {
            findings.push('Critical vulnerabilities detected');
            score += 10;
        } else if (output.includes('high')) {
            findings.push('High severity vulnerabilities detected');
            score += 7;
        } else if (output.includes('medium')) {
            findings.push('Medium severity vulnerabilities detected');
            score += 5;
        }

        return { findings, score };
    }

    analyzeGenericResults(output) {
        const findings = [];
        let score = 0;

        if (output.includes('error') || output.includes('failed')) {
            findings.push('Scan completed with errors');
            score += 1;
        } else {
            findings.push('Scan completed successfully');
        }

        return { findings, score };
    }

    scoreToRiskLevel(score) {
        if (score >= 10) return 'CRITICAL';
        if (score >= 7) return 'HIGH';
        if (score >= 5) return 'MEDIUM';
        if (score >= 3) return 'LOW';
        return 'INFO';
    }

    calculateOverallRisk(categories) {
        let totalWeightedScore = 0;
        let totalWeight = 0;

        for (const [categoryName, categoryData] of Object.entries(categories)) {
            const categoryInfo = this.categories[categoryName];
            if (categoryInfo && categoryData.riskScore !== undefined) {
                totalWeightedScore += categoryData.riskScore * categoryInfo.weight;
                totalWeight += categoryInfo.weight;
            }
        }

        const averageScore = totalWeight > 0 ? totalWeightedScore / totalWeight : 0;
        return this.scoreToRiskLevel(averageScore);
    }

    calculateRiskScore(categories) {
        let totalScore = 0;
        let categoryCount = 0;

        for (const categoryData of Object.values(categories)) {
            if (categoryData.riskScore !== undefined) {
                totalScore += categoryData.riskScore;
                categoryCount++;
            }
        }

        return categoryCount > 0 ? Math.round(totalScore / categoryCount) : 0;
    }

    generateRecommendations(categories, results) {
        const recommendations = [];

        // Check for failed scans
        const failedScans = results.filter(r => r.status === 'FAILED' || r.error);
        if (failedScans.length > 0) {
            recommendations.push({
                priority: 'HIGH',
                category: 'System Configuration',
                recommendation: 'Install missing security tools and verify system configuration',
                details: `${failedScans.length} scans failed due to missing tools or configuration issues`
            });
        }

        // Category-specific recommendations
        for (const [categoryName, categoryData] of Object.entries(categories)) {
            if (categoryData.riskLevel === 'HIGH' || categoryData.riskLevel === 'CRITICAL') {
                recommendations.push({
                    priority: categoryData.riskLevel,
                    category: categoryName,
                    recommendation: `Address ${categoryData.riskLevel.toLowerCase()} risk findings in ${categoryName}`,
                    details: categoryData.findings.join('; ')
                });
            }
        }

        return recommendations;
    }
}

class BlackGReporter {
    constructor() {
        this.reportsDir = './scan_reports';
        this.templatesDir = './report template';
        this.complianceFrameworks = {
            'OWASP': {
                name: 'OWASP Top 10',
                categories: ['A01:2021', 'A02:2021', 'A03:2021', 'A04:2021', 'A05:2021', 'A06:2021', 'A07:2021', 'A08:2021', 'A09:2021', 'A10:2021']
            },
            'NIST': {
                name: 'NIST Cybersecurity Framework',
                categories: ['Identify', 'Protect', 'Detect', 'Respond', 'Recover']
            },
            'ISO27001': {
                name: 'ISO 27001:2022',
                categories: ['A.5', 'A.6', 'A.7', 'A.8', 'A.9', 'A.10', 'A.11', 'A.12', 'A.13', 'A.14']
            }
        };

        // Ensure reports directory exists
        if (!fs.existsSync(this.reportsDir)) {
            fs.mkdirSync(this.reportsDir, { recursive: true });
        }
    }

    async generateReport(reportData) {
        try {
            console.log(`${colors.cyan}[+] Generating comprehensive reports...${colors.reset}`);

            // Enhanced report data with compliance mapping
            const enhancedData = await this.enhanceReportData(reportData);

            // Generate multiple formats
            const reports = await Promise.all([
                this.generateJSONReport(enhancedData),
                this.generateMarkdownReport(enhancedData),
                this.generateExecutiveSummary(enhancedData),
                this.generateComplianceReport(enhancedData)
            ]);

            // Try to generate DOCX if template exists
            if (fs.existsSync(path.join(this.templatesDir, 'template report.docx'))) {
                reports.push(await this.generateDOCXReport(enhancedData));
            }

            console.log(`${colors.green}[✓] Generated ${reports.length} report formats${colors.reset}`);
            return reports;

        } catch (error) {
            throw new Error(`Report generation failed: ${error.message}`);
        }
    }

    async enhanceReportData(data) {
        const enhanced = {
            ...data,
            metadata: {
                generator: 'Black-G CLI Enhanced',
                version: '2.0.0',
                generated: new Date().toISOString(),
                analyst: process.env.USER || 'Unknown',
                framework_version: '2.0.0'
            },
            executive_summary: this.generateExecutiveSummaryData(data),
            compliance_mapping: this.mapToComplianceFrameworks(data),
            risk_matrix: this.generateRiskMatrix(data),
            recommendations: this.generatePrioritizedRecommendations(data),
            metrics: this.calculateSecurityMetrics(data)
        };

        return enhanced;
    }

    generateExecutiveSummaryData(data) {
        const totalFindings = data.analysis?.detailedFindings?.length || 0;
        const criticalFindings = data.analysis?.detailedFindings?.filter(f => f.severity === 'CRITICAL').length || 0;
        const highFindings = data.analysis?.detailedFindings?.filter(f => f.severity === 'HIGH').length || 0;

        return {
            target: data.target,
            scan_date: data.startTime,
            overall_risk: data.analysis?.overallRisk || 'UNKNOWN',
            total_findings: totalFindings,
            critical_findings: criticalFindings,
            high_findings: highFindings,
            success_rate: data.analysis?.executionSummary?.successRate || 0,
            key_concerns: this.extractKeyConcerns(data),
            business_impact: this.assessBusinessImpact(data.analysis?.overallRisk)
        };
    }

    mapToComplianceFrameworks(data) {
        const mapping = {};

        for (const [framework, info] of Object.entries(this.complianceFrameworks)) {
            mapping[framework] = {
                name: info.name,
                applicable_controls: this.mapFindingsToFramework(data, framework),
                compliance_score: this.calculateComplianceScore(data, framework),
                gaps_identified: this.identifyComplianceGaps(data, framework)
            };
        }

        return mapping;
    }

    generateRiskMatrix(data) {
        const matrix = {
            'CRITICAL': { count: 0, findings: [] },
            'HIGH': { count: 0, findings: [] },
            'MEDIUM': { count: 0, findings: [] },
            'LOW': { count: 0, findings: [] },
            'INFO': { count: 0, findings: [] }
        };

        if (data.analysis?.detailedFindings) {
            for (const finding of data.analysis.detailedFindings) {
                const severity = finding.severity || 'INFO';
                if (matrix[severity]) {
                    matrix[severity].count++;
                    matrix[severity].findings.push(finding);
                }
            }
        }

        return matrix;
    }

    generatePrioritizedRecommendations(data) {
        const recommendations = [];

        // Critical security issues
        if (data.analysis?.detailedFindings) {
            const criticalFindings = data.analysis.detailedFindings.filter(f => f.severity === 'CRITICAL');
            if (criticalFindings.length > 0) {
                recommendations.push({
                    priority: 'IMMEDIATE',
                    category: 'Critical Vulnerabilities',
                    action: 'Address critical security vulnerabilities immediately',
                    timeline: '24-48 hours',
                    impact: 'HIGH',
                    effort: 'HIGH',
                    findings_count: criticalFindings.length
                });
            }
        }

        // Tool availability issues
        const failedCommands = data.results?.filter(r => r.status === 'FAILED').length || 0;
        if (failedCommands > 0) {
            recommendations.push({
                priority: 'HIGH',
                category: 'Infrastructure',
                action: 'Install missing security tools and fix configuration issues',
                timeline: '1-2 weeks',
                impact: 'MEDIUM',
                effort: 'LOW',
                findings_count: failedCommands
            });
        }

        // SSL/TLS improvements
        if (data.analysis?.categories?.['SSL/TLS Certificate Analysis']?.riskLevel === 'HIGH') {
            recommendations.push({
                priority: 'HIGH',
                category: 'SSL/TLS Security',
                action: 'Update SSL/TLS configuration and certificates',
                timeline: '1 week',
                impact: 'MEDIUM',
                effort: 'MEDIUM',
                findings_count: 1
            });
        }

        return recommendations.sort((a, b) => {
            const priorityOrder = { 'IMMEDIATE': 0, 'HIGH': 1, 'MEDIUM': 2, 'LOW': 3 };
            return priorityOrder[a.priority] - priorityOrder[b.priority];
        });
    }

    calculateSecurityMetrics(data) {
        const metrics = {
            attack_surface_score: 0,
            security_posture_score: 0,
            compliance_readiness: 0,
            remediation_complexity: 'MEDIUM',
            estimated_remediation_time: '2-4 weeks'
        };

        // Calculate attack surface score (0-100, lower is better)
        const totalFindings = data.analysis?.detailedFindings?.length || 0;
        const criticalFindings = data.analysis?.detailedFindings?.filter(f => f.severity === 'CRITICAL').length || 0;
        const highFindings = data.analysis?.detailedFindings?.filter(f => f.severity === 'HIGH').length || 0;

        metrics.attack_surface_score = Math.min(100, (criticalFindings * 25) + (highFindings * 10) + (totalFindings * 2));

        // Calculate security posture score (0-100, higher is better)
        const successRate = data.analysis?.executionSummary?.successRate || 0;
        const riskScore = data.analysis?.riskScore || 10;
        metrics.security_posture_score = Math.max(0, 100 - riskScore * 10) * (successRate / 100);

        // Calculate compliance readiness (0-100, higher is better)
        metrics.compliance_readiness = Math.max(0, 100 - (criticalFindings * 20) - (highFindings * 10));

        return metrics;
    }

    extractKeyConcerns(data) {
        const concerns = [];

        if (data.analysis?.detailedFindings) {
            const criticalFindings = data.analysis.detailedFindings.filter(f => f.severity === 'CRITICAL');
            if (criticalFindings.length > 0) {
                concerns.push(`${criticalFindings.length} critical security vulnerabilities detected`);
            }

            const openPorts = data.analysis.detailedFindings.filter(f => f.type === 'open_port');
            if (openPorts.length > 5) {
                concerns.push(`${openPorts.length} open ports detected - potential attack vectors`);
            }
        }

        const failedScans = data.results?.filter(r => r.status === 'FAILED').length || 0;
        if (failedScans > 0) {
            concerns.push(`${failedScans} security scans failed - incomplete assessment`);
        }

        return concerns.slice(0, 5); // Top 5 concerns
    }

    assessBusinessImpact(riskLevel) {
        const impacts = {
            'CRITICAL': 'Immediate threat to business operations and data security. Potential for significant financial loss, regulatory penalties, and reputational damage.',
            'HIGH': 'Significant security risks that could lead to data breaches, service disruption, and compliance violations.',
            'MEDIUM': 'Moderate security risks that should be addressed to maintain security posture and prevent escalation.',
            'LOW': 'Minor security issues that represent minimal immediate risk but should be addressed as part of ongoing security maintenance.',
            'UNKNOWN': 'Unable to assess business impact due to incomplete security assessment. Recommend completing full evaluation.'
        };

        return impacts[riskLevel] || impacts['UNKNOWN'];
    }

    mapFindingsToFramework(data, framework) {
        // Simplified mapping - in production, this would be more sophisticated
        const mappings = {
            'OWASP': {
                'open_port': ['A05:2021 - Security Misconfiguration'],
                'ssl_vulnerability': ['A02:2021 - Cryptographic Failures'],
                'vulnerability': ['A06:2021 - Vulnerable and Outdated Components']
            },
            'NIST': {
                'open_port': ['Protect - Access Control'],
                'ssl_vulnerability': ['Protect - Data Security'],
                'vulnerability': ['Identify - Asset Management']
            },
            'ISO27001': {
                'open_port': ['A.9 - Access Control'],
                'ssl_vulnerability': ['A.10 - Cryptography'],
                'vulnerability': ['A.12 - Operations Security']
            }
        };

        const applicable = [];
        if (data.analysis?.detailedFindings && mappings[framework]) {
            for (const finding of data.analysis.detailedFindings) {
                const controls = mappings[framework][finding.type];
                if (controls) {
                    applicable.push(...controls);
                }
            }
        }

        return [...new Set(applicable)]; // Remove duplicates
    }

    calculateComplianceScore(data, framework) {
        // Simplified compliance scoring
        const totalControls = this.complianceFrameworks[framework].categories.length;
        const applicableControls = this.mapFindingsToFramework(data, framework).length;
        const criticalFindings = data.analysis?.detailedFindings?.filter(f => f.severity === 'CRITICAL').length || 0;

        let score = 100;
        score -= (applicableControls / totalControls) * 50; // Reduce score for applicable controls with issues
        score -= criticalFindings * 10; // Reduce score for critical findings

        return Math.max(0, Math.round(score));
    }

    identifyComplianceGaps(data, framework) {
        const gaps = [];
        const applicableControls = this.mapFindingsToFramework(data, framework);

        if (applicableControls.length > 0) {
            gaps.push(`${applicableControls.length} ${framework} controls require attention`);
        }

        return gaps;
    }

    async generateExecutiveSummary(data) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `${timestamp}_executive-summary.md`;
        const filepath = path.join(this.reportsDir, filename);

        const summary = `# Executive Summary - Security Assessment

## Assessment Overview
- **Target**: ${data.target}
- **Assessment Date**: ${new Date(data.startTime).toLocaleDateString()}
- **Overall Risk Level**: ${data.executive_summary.overall_risk}
- **Business Impact**: ${data.executive_summary.business_impact}

## Key Findings
- **Total Security Issues**: ${data.executive_summary.total_findings}
- **Critical Issues**: ${data.executive_summary.critical_findings}
- **High Priority Issues**: ${data.executive_summary.high_findings}
- **Assessment Success Rate**: ${data.executive_summary.success_rate}%

## Risk Assessment
${this.generateRiskSummaryTable(data.risk_matrix)}

## Immediate Actions Required
${data.recommendations.filter(r => r.priority === 'IMMEDIATE').map(r =>
    `- **${r.category}**: ${r.action} (Timeline: ${r.timeline})`
).join('\n')}

## Business Impact Assessment
${data.executive_summary.business_impact}

## Key Security Concerns
${data.executive_summary.key_concerns.map(concern => `- ${concern}`).join('\n')}

## Compliance Status
${Object.entries(data.compliance_mapping).map(([framework, info]) =>
    `- **${info.name}**: ${info.compliance_score}% compliant`
).join('\n')}

## Recommendations Summary
${data.recommendations.slice(0, 5).map((r, i) =>
    `${i + 1}. **${r.category}** (${r.priority}): ${r.action}`
).join('\n')}

---
*Generated by Black-G CLI Enhanced v2.0.0*
`;

        fs.writeFileSync(filepath, summary);
        console.log(`${colors.green}[✓] Executive summary saved: ${filename}${colors.reset}`);
        return filepath;
    }

    async generateComplianceReport(data) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `${timestamp}_compliance-report.md`;
        const filepath = path.join(this.reportsDir, filename);

        const report = `# Compliance Assessment Report

## Assessment Details
- **Target**: ${data.target}
- **Assessment Date**: ${new Date(data.startTime).toLocaleDateString()}
- **Frameworks Evaluated**: ${Object.keys(data.compliance_mapping).join(', ')}

## Compliance Framework Analysis

${Object.entries(data.compliance_mapping).map(([framework, info]) => `
### ${info.name}
- **Compliance Score**: ${info.compliance_score}%
- **Applicable Controls**: ${info.applicable_controls.length}
- **Gaps Identified**: ${info.gaps_identified.length}

#### Applicable Controls
${info.applicable_controls.map(control => `- ${control}`).join('\n')}

#### Compliance Gaps
${info.gaps_identified.map(gap => `- ${gap}`).join('\n')}
`).join('\n')}

## Security Metrics
- **Attack Surface Score**: ${data.metrics.attack_surface_score}/100 (lower is better)
- **Security Posture Score**: ${data.metrics.security_posture_score}/100 (higher is better)
- **Compliance Readiness**: ${data.metrics.compliance_readiness}%
- **Estimated Remediation Time**: ${data.metrics.estimated_remediation_time}

## Detailed Findings by Severity
${this.generateFindingsByCategory(data.risk_matrix)}

## Remediation Roadmap
${data.recommendations.map((r, i) => `
### ${i + 1}. ${r.category} (${r.priority} Priority)
- **Action**: ${r.action}
- **Timeline**: ${r.timeline}
- **Impact**: ${r.impact}
- **Effort**: ${r.effort}
- **Findings**: ${r.findings_count}
`).join('\n')}

---
*Generated by Black-G CLI Enhanced v2.0.0*
`;

        fs.writeFileSync(filepath, report);
        console.log(`${colors.green}[✓] Compliance report saved: ${filename}${colors.reset}`);
        return filepath;
    }

    generateRiskSummaryTable(riskMatrix) {
        return `
| Risk Level | Count | Percentage |
|------------|-------|------------|
| Critical   | ${riskMatrix.CRITICAL.count} | ${this.calculatePercentage(riskMatrix.CRITICAL.count, this.getTotalFindings(riskMatrix))}% |
| High       | ${riskMatrix.HIGH.count} | ${this.calculatePercentage(riskMatrix.HIGH.count, this.getTotalFindings(riskMatrix))}% |
| Medium     | ${riskMatrix.MEDIUM.count} | ${this.calculatePercentage(riskMatrix.MEDIUM.count, this.getTotalFindings(riskMatrix))}% |
| Low        | ${riskMatrix.LOW.count} | ${this.calculatePercentage(riskMatrix.LOW.count, this.getTotalFindings(riskMatrix))}% |
| Info       | ${riskMatrix.INFO.count} | ${this.calculatePercentage(riskMatrix.INFO.count, this.getTotalFindings(riskMatrix))}% |
`;
    }

    generateFindingsByCategory(riskMatrix) {
        return Object.entries(riskMatrix).map(([severity, data]) => `
#### ${severity} Risk Findings (${data.count})
${data.findings.map(finding => `- ${finding.description || finding.type}`).join('\n')}
`).join('\n');
    }

    getTotalFindings(riskMatrix) {
        return Object.values(riskMatrix).reduce((total, category) => total + category.count, 0);
    }

    calculatePercentage(count, total) {
        return total > 0 ? Math.round((count / total) * 100) : 0;
    }

    async generateJSONReport(data) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `${timestamp}_black-g-report.json`;
        const filepath = path.join(this.reportsDir, filename);

        const reportData = {
            metadata: {
                generator: 'Black-G CLI',
                version: '1.0.0',
                generatedAt: new Date().toISOString(),
                sessionId: data.sessionId,
                target: data.target
            },
            scanDetails: {
                startTime: data.startTime,
                endTime: data.endTime,
                totalCommands: data.commands.length,
                successfulCommands: data.results.filter(r => !r.error).length,
                failedCommands: data.results.filter(r => r.error).length
            },
            results: data.results,
            aiAnalysis: data.aiAnalysis
        };

        fs.writeFileSync(filepath, JSON.stringify(reportData, null, 2));
        console.log(`${colors.green}[✓]${colors.reset} JSON report saved: ${filepath}`);
    }

    async generateMarkdownReport(data) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `${timestamp}_black-g-report.md`;
        const filepath = path.join(this.reportsDir, filename);

        const markdown = `# Black-G Security Assessment Report

**Target:** ${data.target}
**Session ID:** ${data.sessionId}
**Generated:** ${new Date().toLocaleString()}
**Duration:** ${data.startTime} - ${data.endTime}

## Executive Summary

This report contains the results of a security assessment performed using Black-G CLI, an AI-driven penetration testing system specialized in Attack Surface Management.

## Scan Details

- **Total Commands Executed:** ${data.commands.length}
- **Successful Commands:** ${data.results.filter(r => !r.error).length}
- **Failed Commands:** ${data.results.filter(r => r.error).length}

## Command Results

${data.results.map((result, index) => `
### ${index + 1}. ${result.command}

**Category:** ${result.category || 'General'}
**Status:** ${result.error ? 'FAILED' : 'SUCCESS'}
**Timestamp:** ${result.timestamp}

${result.error ? `**Error:** ${result.error}` : '**Output:** Command executed successfully'}

---
`).join('')}

## AI Analysis

${data.aiAnalysis}

---

*Report generated by Black-G CLI v1.0.0*
*© 2025 - Automated Security Assessment*
`;

        fs.writeFileSync(filepath, markdown);
        console.log(`${colors.green}[✓]${colors.reset} Markdown report saved: ${filepath}`);
    }

    async generateDOCXReport(data) {
        // This would integrate with the existing DOCX generation system
        console.log(`${colors.blue}[i]${colors.reset} DOCX report generation would integrate with existing template system`);
    }
}

// Main execution
if (require.main === module) {
    // Command line arguments are handled at the top of the file
    // If we reach here, start the interactive mode

    // Handle graceful shutdown
    process.on('SIGINT', () => {
        console.log(`\n🛑 Received interrupt signal. Shutting down gracefully...`);
        process.exit(0);
    });

    process.on('SIGTERM', () => {
        console.log(`\n🛑 Received termination signal. Shutting down gracefully...`);
        process.exit(0);
    });

    // Start Black-G CLI in interactive mode
    try {
        new BlackGCLI();
    } catch (error) {
        console.error(`[-] Failed to start Black-G CLI: ${error.message}`);
        process.exit(1);
    }
}
